{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NEXT_PUBLIC_API_URL"], "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "inputs": [".env"], "cache": false}, "lint": {}, "type-check": {}, "dev": {"cache": false, "persistent": true, "inputs": [".env"]}, "start": {}, "clean": {"cache": false}}}