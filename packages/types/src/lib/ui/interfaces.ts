import { DateRange } from 'react-day-picker';

import { ProgressProps } from '@radix-ui/react-progress';
import { ISeparator } from '../atoms/interfaces';

// Format: { THEME_NAME: CSS_SELECTOR }
export const THEMES = { light: '', dark: '.dark' } as const;

export type ChartConfig = {
	[k in string]: {
		label?: React.ReactNode;
		icon?: React.ComponentType;
	} & ({ color?: string; theme?: never } | { color?: never; theme: Record<keyof typeof THEMES, string> });
};

export type ChartContextProps = {
	config: ChartConfig;
};

export interface ISelectValue {
	value: any;
	label: string;
	icon?: React.ReactNode;
}

export interface IDatePickerProps {
	placeholder?: string;
	icon?: boolean;
	date?: Date;
	className?: string | undefined;
	setDate?: React.Dispatch<React.SetStateAction<Date | undefined>>;
}

export interface IDateRangePickerProps {
	date?: DateRange;
	setDate?: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
	className?: string | undefined;
	size?: 'default' | 'sm' | 'lg' | 'icon';
}

export interface ITimerDisplayerProps extends React.HTMLAttributes<HTMLSpanElement> {
	separator?: ISeparator;
	fontSize?: React.CSSProperties['fontSize'];
	fontWeight?: React.CSSProperties['fontWeight'];
	fontFamily?: React.CSSProperties['fontFamily'];
}

export interface IClocProgressProps extends ProgressProps {
	className?: string;
}

export interface IChartProps {
	data: Object[];
	draggable?: boolean;
	config?: ChartConfig;
	title?: React.ReactElement | string;
	description?: React.ReactElement | string;
	layout?: 'horizontal' | 'vertical';
	label?: boolean;
	footer?: React.ReactElement | string;
	color?: string;
	type?: string | undefined;
	className?: string;
}

export interface Member {
	label: string;
	progress?: number;
	time?: string;
	color: string;
}
