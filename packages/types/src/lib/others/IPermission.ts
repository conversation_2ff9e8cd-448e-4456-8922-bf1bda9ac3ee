export interface IPermission {
	archivedAt: string | null;
	createdAt: string | null;
	deletedAt: string | null;
	description: string | null;
	enabled: boolean;
	id: string;
	isActive: boolean;
	isArchived: boolean;
	permission: PermissionsEnum;
	roleId: string;
	tenantId: string;
	updatedAt: string | null;
}

export enum PermissionsEnum {
	ADMIN_DASHBOARD_VIEW = 'ADMIN_DASHBOARD_VIEW',
	TEAM_DASHBOARD = 'TEAM_DASHBOARD',
	PROJECT_MANAGEMENT_DASHBOARD = 'PROJECT_MANAGEMENT_DASHBOARD',
	TIME_TRACKING_DASHBOARD = 'TIME_TRACKING_DASHBOARD',
	ACCOUNTING_DASHBOARD = 'ACCOUNTING_DASHBOARD',
	HUMAN_RESOURCE_DASHBOARD = 'HUMAN_RESOURCE_DASHBOARD',
	ORG_PAYMENT_VIEW = 'ORG_PAYMENT_VIEW',
	ORG_PAYMENT_ADD_EDIT = 'ORG_PAYMENT_ADD_EDIT',
	ORG_INCOMES_VIEW = 'ORG_INCOMES_VIEW',
	ORG_INCOMES_EDIT = 'ORG_INCOMES_EDIT',
	ORG_EXPENSES_VIEW = 'ORG_EXPENSES_VIEW',
	ORG_EXPENSES_EDIT = 'ORG_EXPENSES_EDIT',
	PROFILE_EDIT = 'PROFILE_EDIT',
	EMPLOYEE_EXPENSES_VIEW = 'EMPLOYEE_EXPENSES_VIEW',
	EMPLOYEE_EXPENSES_EDIT = 'EMPLOYEE_EXPENSES_EDIT',
	ORG_PROPOSALS_VIEW = 'ORG_PROPOSALS_VIEW',
	ORG_PROPOSALS_EDIT = 'ORG_PROPOSALS_EDIT',
	ORG_PROPOSAL_TEMPLATES_VIEW = 'ORG_PROPOSAL_TEMPLATES_VIEW',
	ORG_PROPOSAL_TEMPLATES_EDIT = 'ORG_PROPOSAL_TEMPLATES_EDIT',
	// Task CRUD Permission
	ORG_TASK_ADD = 'ORG_TASK_ADD',
	ORG_TASK_VIEW = 'ORG_TASK_VIEW',
	ORG_TASK_EDIT = 'ORG_TASK_EDIT',
	ORG_TASK_DELETE = 'ORG_TASK_DELETE',
	// Employee CRUD Permissions
	ORG_EMPLOYEES_ADD = 'ORG_EMPLOYEES_ADD',
	ORG_EMPLOYEES_VIEW = 'ORG_EMPLOYEES_VIEW',
	ORG_EMPLOYEES_EDIT = 'ORG_EMPLOYEES_EDIT',
	ORG_EMPLOYEES_DELETE = 'ORG_EMPLOYEES_DELETE',
	// Member View Permissions
	ORG_MEMBERS_VIEW = 'ORG_MEMBERS_VIEW',
	ORG_CANDIDATES_VIEW = 'ORG_CANDIDATES_VIEW',
	ORG_CANDIDATES_EDIT = 'ORG_CANDIDATES_EDIT',
	ORG_CANDIDATES_INTERVIEW_EDIT = 'ORG_CANDIDATES_INTERVIEW_EDIT',
	ORG_CANDIDATES_INTERVIEW_VIEW = 'ORG_CANDIDATES_INTERVIEW_VIEW',
	ORG_CANDIDATES_DOCUMENTS_VIEW = 'ORG_CANDIDATES_DOCUMENTS_VIEW',
	ORG_CANDIDATES_TASK_EDIT = 'ORG_CANDIDATES_TASK_EDIT',
	ORG_CANDIDATES_FEEDBACK_EDIT = 'ORG_CANDIDATES_FEEDBACK_EDIT',
	ORG_INVENTORY_PRODUCT_EDIT = 'ORG_INVENTORY_PRODUCT_EDIT',
	ORG_INVENTORY_VIEW = 'ORG_INVENTORY_VIEW',
	ORG_TAGS_ADD = 'ORG_TAGS_ADD',
	ORG_TAGS_VIEW = 'ORG_TAGS_VIEW',
	ORG_TAGS_EDIT = 'ORG_TAGS_EDIT',
	ORG_TAGS_DELETE = 'ORG_TAGS_DELETE',
	ORG_TAG_TYPES_ADD = 'ORG_TAG_TYPES_ADD',
	ORG_TAG_TYPES_VIEW = 'ORG_TAG_TYPES_VIEW',
	ORG_TAG_TYPES_EDIT = 'ORG_TAG_TYPES_EDIT',
	ORG_TAG_TYPES_DELETE = 'ORG_TAG_TYPES_DELETE',
	ORG_USERS_VIEW = 'ORG_USERS_VIEW',
	ORG_USERS_EDIT = 'ORG_USERS_EDIT',
	ORG_INVITE_VIEW = 'ORG_INVITE_VIEW',
	ORG_INVITE_EDIT = 'ORG_INVITE_EDIT',
	ALL_ORG_VIEW = 'ALL_ORG_VIEW',
	ALL_ORG_EDIT = 'ALL_ORG_EDIT',
	/** Time Off Policy CRUD Permissions */
	TIME_OFF_POLICY_ADD = 'TIME_OFF_POLICY_ADD',
	TIME_OFF_POLICY_VIEW = 'TIME_OFF_POLICY_VIEW',
	TIME_OFF_POLICY_EDIT = 'TIME_OFF_POLICY_EDIT',
	TIME_OFF_POLICY_DELETE = 'TIME_OFF_POLICY_DELETE',
	/** Time Off CRUD Permissions */
	TIME_OFF_ADD = 'TIME_OFF_ADD',
	TIME_OFF_VIEW = 'TIME_OFF_VIEW',
	TIME_OFF_EDIT = 'TIME_OFF_EDIT',
	TIME_OFF_DELETE = 'TIME_OFF_DELETE',
	REQUEST_APPROVAL_VIEW = 'REQUEST_APPROVAL_VIEW',
	REQUEST_APPROVAL_EDIT = 'REQUEST_APPROVAL_EDIT',
	APPROVAL_POLICY_VIEW = 'APPROVALS_POLICY_VIEW',
	APPROVAL_POLICY_EDIT = 'APPROVALS_POLICY_EDIT',
	CHANGE_SELECTED_EMPLOYEE = 'CHANGE_SELECTED_EMPLOYEE',
	CHANGE_SELECTED_CANDIDATE = 'CHANGE_SELECTED_CANDIDATE',
	CHANGE_SELECTED_ORGANIZATION = 'CHANGE_SELECTED_ORGANIZATION',
	CHANGE_ROLES_PERMISSIONS = 'CHANGE_ROLES_PERMISSIONS',
	ACCESS_PRIVATE_PROJECTS = 'ACCESS_PRIVATE_PROJECTS',
	TIMESHEET_EDIT_TIME = 'TIMESHEET_EDIT_TIME',
	SUPER_ADMIN_EDIT = 'SUPER_ADMIN_EDIT',
	PUBLIC_PAGE_EDIT = 'PUBLIC_PAGE_EDIT',
	INVOICES_VIEW = 'INVOICES_VIEW',
	INVOICES_EDIT = 'INVOICES_EDIT',
	ESTIMATES_VIEW = 'ESTIMATES_VIEW',
	ESTIMATES_EDIT = 'ESTIMATES_EDIT',
	ORG_CANDIDATES_INTERVIEWERS_EDIT = 'ORG_CANDIDATES_INTERVIEWERS_EDIT',
	ORG_CANDIDATES_INTERVIEWERS_VIEW = 'ORG_CANDIDATES_INTERVIEWERS_VIEW',
	VIEW_ALL_EMAILS = 'VIEW_ALL_EMAILS',
	VIEW_ALL_EMAIL_TEMPLATES = 'VIEW_ALL_EMAIL_TEMPLATES',
	ORG_HELP_CENTER_EDIT = 'ORG_HELP_CENTER_EDIT',
	VIEW_SALES_PIPELINES = 'VIEW_SALES_PIPELINES',
	EDIT_SALES_PIPELINES = 'EDIT_SALES_PIPELINES',
	CAN_APPROVE_TIMESHEET = 'CAN_APPROVE_TIMESHEET',
	ORG_SPRINT_ADD = 'ORG_SPRINT_ADD',
	ORG_SPRINT_VIEW = 'ORG_SPRINT_VIEW',
	ORG_SPRINT_EDIT = 'ORG_SPRINT_EDIT',
	ORG_SPRINT_DELETE = 'ORG_SPRINT_DELETE',
	ORG_CONTACT_EDIT = 'ORG_CONTACT_EDIT',
	ORG_CONTACT_VIEW = 'ORG_CONTACT_VIEW',
	ORG_PROJECT_ADD = 'ORG_PROJECT_ADD',
	ORG_PROJECT_VIEW = 'ORG_PROJECT_VIEW',
	ORG_PROJECT_EDIT = 'ORG_PROJECT_EDIT',
	ORG_PROJECT_DELETE = 'ORG_PROJECT_DELETE',
	/** Organization Team */
	ORG_TEAM_ADD = 'ORG_TEAM_ADD',
	ORG_TEAM_VIEW = 'ORG_TEAM_VIEW',
	ORG_TEAM_EDIT = 'ORG_TEAM_EDIT',
	ORG_TEAM_EDIT_ACTIVE_TASK = 'ORG_TEAM_EDIT_ACTIVE_TASK',
	ORG_TEAM_DELETE = 'ORG_TEAM_DELETE',
	ORG_TEAM_REMOVE_ACCOUNT_AS_MEMBER = 'ORG_TEAM_REMOVE_ACCOUNT_AS_MEMBER',
	/** Organization Team Join Request Permissions */
	ORG_TEAM_JOIN_REQUEST_VIEW = 'ORG_TEAM_JOIN_REQUEST_VIEW',
	ORG_TEAM_JOIN_REQUEST_EDIT = 'ORG_TEAM_JOIN_REQUEST_EDIT',
	/** Organization Task Setting */
	ORG_TASK_SETTING = 'ORG_TASK_SETTING',
	ORG_CONTRACT_EDIT = 'ORG_CONTRACT_EDIT',
	EVENT_TYPES_VIEW = 'EVENT_TYPES_VIEW',
	TIME_TRACKER = 'TIME_TRACKER',
	TENANT_ADD_EXISTING_USER = 'TENANT_ADD_EXISTING_USER',
	/** Integration CRUD Permissions Start */
	INTEGRATION_ADD = 'INTEGRATION_ADD',
	INTEGRATION_VIEW = 'INTEGRATION_VIEW',
	INTEGRATION_EDIT = 'INTEGRATION_EDIT',
	INTEGRATION_DELETE = 'INTEGRATION_DELETE',
	/** Integration CRUD Permissions End */
	FILE_STORAGE_VIEW = 'FILE_STORAGE_VIEW',
	PAYMENT_GATEWAY_VIEW = 'PAYMENT_GATEWAY_VIEW',
	SMS_GATEWAY_VIEW = 'SMS_GATEWAY_VIEW',
	CUSTOM_SMTP_VIEW = 'CUSTOM_SMTP_VIEW',
	IMPORT_ADD = 'IMPORT_ADD',
	EXPORT_ADD = 'EXPORT_ADD',
	MIGRATE_GAUZY_CLOUD = 'MIGRATE_GAUZY_CLOUD',
	/** Jobs Permissions Start */
	ORG_JOB_SEARCH = 'ORG_JOB_SEARCH',
	ORG_JOB_APPLY = 'ORG_JOB_APPLY',
	ORG_JOB_EDIT = 'ORG_JOB_EDIT',
	ORG_JOB_EMPLOYEE_VIEW = 'ORG_JOB_EMPLOYEE_VIEW',
	ORG_JOB_MATCHING_VIEW = 'ORG_JOB_MATCHING_VIEW',
	/** Jobs Permissions End */
	INVENTORY_GALLERY_ADD = 'INVENTORY_GALLERY_ADD',
	INVENTORY_GALLERY_VIEW = 'INVENTORY_GALLERY_VIEW',
	INVENTORY_GALLERY_EDIT = 'INVENTORY_GALLERY_EDIT',
	INVENTORY_GALLERY_DELETE = 'INVENTORY_GALLERY_DELETE',
	MEDIA_GALLERY_ADD = 'MEDIA_GALLERY_ADD',
	MEDIA_GALLERY_VIEW = 'MEDIA_GALLERY_VIEW',
	MEDIA_GALLERY_EDIT = 'MEDIA_GALLERY_EDIT',
	MEDIA_GALLERY_DELETE = 'MEDIA_GALLERY_DELETE',
	ORG_EQUIPMENT_VIEW = 'ORG_EQUIPMENT_VIEW',
	ORG_EQUIPMENT_EDIT = 'ORG_EQUIPMENT_EDIT',
	ORG_EQUIPMENT_SHARING_VIEW = 'ORG_EQUIPMENT_SHARING_VIEW',
	ORG_EQUIPMENT_SHARING_EDIT = 'ORG_EQUIPMENT_SHARING_EDIT',
	EQUIPMENT_MAKE_REQUEST = 'EQUIPMENT_MAKE_REQUEST',
	EQUIPMENT_APPROVE_REQUEST = 'EQUIPMENT_APPROVE_REQUEST',
	/** Equipment Sharing Policy CRUD Permissions Start */
	EQUIPMENT_SHARING_POLICY_ADD = 'EQUIPMENT_SHARING_POLICY_ADD',
	EQUIPMENT_SHARING_POLICY_VIEW = 'EQUIPMENT_SHARING_POLICY_VIEW',
	EQUIPMENT_SHARING_POLICY_EDIT = 'EQUIPMENT_SHARING_POLICY_EDIT',
	EQUIPMENT_SHARING_POLICY_DELETE = 'EQUIPMENT_SHARING_POLICY_DELETE',
	/** Equipment Sharing Policy CRUD Permissions End */
	ORG_PRODUCT_TYPES_VIEW = 'ORG_PRODUCT_TYPES_VIEW',
	ORG_PRODUCT_TYPES_EDIT = 'ORG_PRODUCT_TYPES_EDIT',
	ORG_PRODUCT_CATEGORIES_VIEW = 'ORG_PRODUCT_CATEGORIES_VIEW',
	ORG_PRODUCT_CATEGORIES_EDIT = 'ORG_PRODUCT_CATEGORIES_EDIT',
	VIEW_ALL_ACCOUNTING_TEMPLATES = 'VIEW_ALL_ACCOUNTING_TEMPLATES',
	ACCESS_DELETE_ACCOUNT = 'ACCESS_DELETE_ACCOUNT',
	ACCESS_DELETE_ALL_DATA = 'ACCESS_DELETE_ALL_DATA',
	TENANT_SETTING = 'TENANT_SETTING', //allow user to access tenant setting
	ALLOW_DELETE_TIME = 'ALLOW_DELETE_TIME',
	ALLOW_MODIFY_TIME = 'ALLOW_MODIFY_TIME',
	ALLOW_MANUAL_TIME = 'ALLOW_MANUAL_TIME',
	DELETE_SCREENSHOTS = 'DELETE_SCREENSHOTS',
	ORG_MEMBER_LAST_LOG_VIEW = 'ORG_MEMBER_LAST_LOG_VIEW',
	/** Daily Plan */
	DAILY_PLAN_CREATE = 'DAILY_PLAN_CREATE',
	DAILY_PLAN_READ = 'DAILY_PLAN_READ',
	DAILY_PLAN_UPDATE = 'DAILY_PLAN_UPDATE',
	DAILY_PLAN_DELETE = 'DAILY_PLAN_DELETE',
	/** Project Module */
	PROJECT_MODULE_CREATE = 'PROJECT_MODULE_CREATE',
	PROJECT_MODULE_READ = 'PROJECT_MODULE_READ',
	PROJECT_MODULE_UPDATE = 'PROJECT_MODULE_UPDATE',
	PROJECT_MODULE_DELETE = 'PROJECT_MODULE_DELETE',
	/** API Call Log */
	API_CALL_LOG_READ = 'API_CALL_LOG_READ',
	API_CALL_LOG_DELETE = 'API_CALL_LOG_DELETE',
	/** Dashboard */
	DASHBOARD_CREATE = 'DASHBOARD_CREATE',
	DASHBOARD_READ = 'DASHBOARD_READ',
	DASHBOARD_UPDATE = 'DASHBOARD_UPDATE',
	DASHBOARD_DELETE = 'DASHBOARD_DELETE'
}

export const PermissionGroups = {
	//Permissions which can be given to any role
	GENERAL: [
		PermissionsEnum.ADMIN_DASHBOARD_VIEW,
		PermissionsEnum.TEAM_DASHBOARD,
		PermissionsEnum.PROJECT_MANAGEMENT_DASHBOARD,
		PermissionsEnum.TIME_TRACKING_DASHBOARD,
		PermissionsEnum.ACCOUNTING_DASHBOARD,
		PermissionsEnum.HUMAN_RESOURCE_DASHBOARD,
		PermissionsEnum.CHANGE_SELECTED_EMPLOYEE,
		PermissionsEnum.CHANGE_SELECTED_CANDIDATE,
		PermissionsEnum.CHANGE_SELECTED_ORGANIZATION,
		/** Integration CRUD Permissions Start */
		PermissionsEnum.INTEGRATION_ADD,
		PermissionsEnum.INTEGRATION_VIEW,
		PermissionsEnum.INTEGRATION_EDIT,
		PermissionsEnum.INTEGRATION_DELETE,
		/** Integration CRUD Permissions End */
		/** Jobs Permissions Start */
		PermissionsEnum.ORG_JOB_APPLY,
		PermissionsEnum.ORG_JOB_SEARCH,
		PermissionsEnum.ORG_JOB_EDIT,
		PermissionsEnum.ORG_JOB_EMPLOYEE_VIEW,
		PermissionsEnum.ORG_JOB_MATCHING_VIEW,
		/** Jobs Permissions End */
		PermissionsEnum.PUBLIC_PAGE_EDIT,
		PermissionsEnum.ORG_PAYMENT_VIEW,
		PermissionsEnum.ORG_PAYMENT_ADD_EDIT,
		PermissionsEnum.ORG_EXPENSES_VIEW,
		PermissionsEnum.ORG_EXPENSES_EDIT,
		PermissionsEnum.EMPLOYEE_EXPENSES_VIEW,
		PermissionsEnum.EMPLOYEE_EXPENSES_EDIT,
		PermissionsEnum.ORG_INCOMES_EDIT,
		PermissionsEnum.ORG_INCOMES_VIEW,
		PermissionsEnum.ORG_PROPOSALS_EDIT,
		PermissionsEnum.ORG_PROPOSALS_VIEW,
		PermissionsEnum.ORG_PROPOSAL_TEMPLATES_VIEW,
		PermissionsEnum.ORG_PROPOSAL_TEMPLATES_EDIT,
		/** Employee CRUD Permissions Start */
		PermissionsEnum.ORG_EMPLOYEES_ADD,
		PermissionsEnum.ORG_EMPLOYEES_VIEW,
		PermissionsEnum.ORG_EMPLOYEES_DELETE,
		/** Employee CRUD Permissions End */
		/** Task CRUD Permissions Start */
		PermissionsEnum.ORG_TASK_ADD,
		PermissionsEnum.ORG_TASK_VIEW,
		PermissionsEnum.ORG_TASK_EDIT,
		PermissionsEnum.ORG_TASK_DELETE,
		/** Task CRUD Permissions End */
		PermissionsEnum.ORG_INVITE_VIEW,
		PermissionsEnum.ORG_INVITE_EDIT,
		/** Time Off Policy CRUD Permissions Start */
		PermissionsEnum.TIME_OFF_POLICY_ADD,
		PermissionsEnum.TIME_OFF_POLICY_VIEW,
		PermissionsEnum.TIME_OFF_POLICY_EDIT,
		PermissionsEnum.TIME_OFF_POLICY_DELETE,
		/** Time Off Policy CRUD Permissions End */
		/** Time Off Permissions Start */
		PermissionsEnum.TIME_OFF_ADD,
		PermissionsEnum.TIME_OFF_VIEW,
		PermissionsEnum.TIME_OFF_EDIT,
		PermissionsEnum.TIME_OFF_DELETE,
		/** Time Off Permissions End */
		PermissionsEnum.APPROVAL_POLICY_EDIT,
		PermissionsEnum.APPROVAL_POLICY_VIEW,
		PermissionsEnum.REQUEST_APPROVAL_EDIT,
		PermissionsEnum.REQUEST_APPROVAL_VIEW,
		PermissionsEnum.ACCESS_PRIVATE_PROJECTS,
		PermissionsEnum.TIMESHEET_EDIT_TIME,
		PermissionsEnum.INVOICES_VIEW,
		PermissionsEnum.INVOICES_EDIT,
		PermissionsEnum.ESTIMATES_VIEW,
		PermissionsEnum.ESTIMATES_EDIT,
		PermissionsEnum.ORG_CANDIDATES_DOCUMENTS_VIEW,
		PermissionsEnum.ORG_CANDIDATES_TASK_EDIT,
		PermissionsEnum.ORG_CANDIDATES_INTERVIEW_EDIT,
		PermissionsEnum.ORG_CANDIDATES_INTERVIEW_VIEW,
		PermissionsEnum.ORG_CANDIDATES_INTERVIEWERS_EDIT,
		PermissionsEnum.ORG_CANDIDATES_INTERVIEWERS_VIEW,
		PermissionsEnum.ORG_CANDIDATES_FEEDBACK_EDIT,
		PermissionsEnum.ORG_INVENTORY_VIEW,
		PermissionsEnum.ORG_INVENTORY_PRODUCT_EDIT,
		PermissionsEnum.ORG_TAGS_EDIT,
		PermissionsEnum.VIEW_ALL_EMAILS,
		PermissionsEnum.VIEW_ALL_EMAIL_TEMPLATES,
		PermissionsEnum.ORG_HELP_CENTER_EDIT,
		PermissionsEnum.VIEW_SALES_PIPELINES,
		PermissionsEnum.EDIT_SALES_PIPELINES,
		PermissionsEnum.CAN_APPROVE_TIMESHEET,
		PermissionsEnum.ORG_SPRINT_ADD,
		PermissionsEnum.ORG_SPRINT_EDIT,
		PermissionsEnum.ORG_SPRINT_VIEW,
		PermissionsEnum.ORG_SPRINT_DELETE,
		PermissionsEnum.ORG_PROJECT_ADD,
		PermissionsEnum.ORG_PROJECT_VIEW,
		PermissionsEnum.ORG_PROJECT_EDIT,
		PermissionsEnum.ORG_PROJECT_DELETE,
		PermissionsEnum.ORG_CONTACT_EDIT,
		PermissionsEnum.ORG_CONTACT_VIEW,
		/** Daily Plan Permissions Start */
		PermissionsEnum.DAILY_PLAN_CREATE,
		PermissionsEnum.DAILY_PLAN_READ,
		PermissionsEnum.DAILY_PLAN_UPDATE,
		PermissionsEnum.DAILY_PLAN_DELETE,
		/** Daily Plan Permissions End */
		/** Project Module Permissions start */
		PermissionsEnum.PROJECT_MODULE_CREATE,
		PermissionsEnum.PROJECT_MODULE_READ,
		PermissionsEnum.PROJECT_MODULE_UPDATE,
		PermissionsEnum.PROJECT_MODULE_DELETE,
		/** Project Module Permissions end */
		/** Dashboard Permissions Start */
		PermissionsEnum.DASHBOARD_CREATE,
		PermissionsEnum.DASHBOARD_READ,
		PermissionsEnum.DASHBOARD_UPDATE,
		PermissionsEnum.DASHBOARD_DELETE,
		/** Dashboard Permissions End */
		/** Organization Team Permissions Start */
		PermissionsEnum.ORG_TEAM_ADD,
		PermissionsEnum.ORG_TEAM_VIEW,
		PermissionsEnum.ORG_TEAM_EDIT,
		PermissionsEnum.ORG_TEAM_DELETE,
		PermissionsEnum.ORG_TEAM_EDIT_ACTIVE_TASK,
		PermissionsEnum.ORG_TEAM_REMOVE_ACCOUNT_AS_MEMBER,
		PermissionsEnum.ORG_TEAM_JOIN_REQUEST_VIEW,
		PermissionsEnum.ORG_TEAM_JOIN_REQUEST_EDIT,
		/** Organization Team Permissions End */
		PermissionsEnum.ORG_TASK_SETTING,
		PermissionsEnum.ORG_CONTRACT_EDIT,
		PermissionsEnum.EVENT_TYPES_VIEW,
		PermissionsEnum.TIME_TRACKER,
		PermissionsEnum.INVENTORY_GALLERY_VIEW,
		PermissionsEnum.INVENTORY_GALLERY_EDIT,
		PermissionsEnum.MEDIA_GALLERY_ADD,
		PermissionsEnum.MEDIA_GALLERY_VIEW,
		PermissionsEnum.MEDIA_GALLERY_EDIT,
		PermissionsEnum.MEDIA_GALLERY_DELETE,
		PermissionsEnum.ORG_EQUIPMENT_VIEW,
		PermissionsEnum.ORG_EQUIPMENT_EDIT,
		PermissionsEnum.ORG_EQUIPMENT_SHARING_VIEW,
		PermissionsEnum.ORG_EQUIPMENT_SHARING_EDIT,
		PermissionsEnum.EQUIPMENT_MAKE_REQUEST,
		PermissionsEnum.EQUIPMENT_APPROVE_REQUEST,
		/**Equipment Sharing Policy Permissions Start */
		PermissionsEnum.EQUIPMENT_SHARING_POLICY_ADD,
		PermissionsEnum.EQUIPMENT_SHARING_POLICY_VIEW,
		PermissionsEnum.EQUIPMENT_SHARING_POLICY_EDIT,
		PermissionsEnum.EQUIPMENT_SHARING_POLICY_DELETE,
		/** Equipment Sharing Policy Permissions End */
		PermissionsEnum.ORG_TAGS_ADD,
		PermissionsEnum.ORG_TAGS_VIEW,
		PermissionsEnum.ORG_TAGS_EDIT,
		PermissionsEnum.ORG_TAGS_DELETE,
		/**Tag Permissions End */
		PermissionsEnum.ORG_TAG_TYPES_ADD,
		PermissionsEnum.ORG_TAG_TYPES_VIEW,
		PermissionsEnum.ORG_TAG_TYPES_EDIT,
		PermissionsEnum.ORG_TAG_TYPES_DELETE,
		/**Tag Type Permissions End */
		PermissionsEnum.ORG_PRODUCT_TYPES_VIEW,
		PermissionsEnum.ORG_PRODUCT_CATEGORIES_VIEW,
		PermissionsEnum.ORG_PRODUCT_CATEGORIES_EDIT,
		PermissionsEnum.VIEW_ALL_ACCOUNTING_TEMPLATES,
		PermissionsEnum.ALLOW_DELETE_TIME,
		PermissionsEnum.ALLOW_MODIFY_TIME,
		PermissionsEnum.ALLOW_MANUAL_TIME,
		PermissionsEnum.DELETE_SCREENSHOTS,
		PermissionsEnum.ACCESS_DELETE_ACCOUNT,
		PermissionsEnum.ORG_MEMBER_LAST_LOG_VIEW
	],

	//Readonly permissions, are only enabled for Super Admin/Admin role
	ADMINISTRATION: [
		PermissionsEnum.ORG_EMPLOYEES_EDIT,
		PermissionsEnum.ORG_CANDIDATES_VIEW,
		PermissionsEnum.ORG_CANDIDATES_EDIT,
		PermissionsEnum.ORG_USERS_VIEW,
		PermissionsEnum.ORG_USERS_EDIT,
		PermissionsEnum.ALL_ORG_VIEW,
		PermissionsEnum.ALL_ORG_EDIT,
		PermissionsEnum.CHANGE_ROLES_PERMISSIONS,
		PermissionsEnum.TENANT_ADD_EXISTING_USER,
		PermissionsEnum.FILE_STORAGE_VIEW,
		PermissionsEnum.PAYMENT_GATEWAY_VIEW,
		PermissionsEnum.SMS_GATEWAY_VIEW,
		PermissionsEnum.CUSTOM_SMTP_VIEW,
		PermissionsEnum.IMPORT_ADD,
		PermissionsEnum.EXPORT_ADD,
		PermissionsEnum.ACCESS_DELETE_ALL_DATA,
		PermissionsEnum.TENANT_SETTING,
		PermissionsEnum.API_CALL_LOG_READ,
		PermissionsEnum.API_CALL_LOG_DELETE
	]
};
