{"extends": "@cloc/typescript-config/base.json", "compilerOptions": {"plugins": [{"name": "next"}], "outDir": "dist", "rootDir": "./src", "module": "ESNext", "target": "es6", "jsx": "react-jsx", "declaration": true, "emitDeclarationOnly": true, "declarationDir": "dist", "strict": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/lib/**/*.ts", "src/lib/**/*.tsx"], "exclude": ["node_modules", ".turbo"]}