@tailwind base;
@tailwind components;
@tailwind utilities;

@import "@cloc/atoms/styles.css";
@import "@cloc/ui/styles.css";

:root {
  --gjs-one-bg: #f7f7f7;
  --gjs-two-color: #636467;
}

.dark {
  --gjs-one-bg: #1c1c1c;
  --gjs-two-color: #EFEFEF;
}

.gjs-one-bg {
  background-color: var(--gjs-one-bg) !important;
}

.gjs-two-color {
  color: var(--gjs-two-color) !important;
}

.component-selected {
    @apply relative;
  }
  .component-selected::after {
    content: ' ';
    @apply border-blue-500 border border-dashed w-full h-full absolute left-0 top-0 pointer-events-none block rounded;
  }

  .transition {
    transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  }

  .cloc-button-default {
    background: linear-gradient(to bottom right, var(--primaryColor), var(--secondaryColor));
    color: var(--primary-foreground);
  }
  .cloc-button-sm {
    height: 40px;
    width: 40px;
  }
  .cloc-button-lg {
    height: 70px;
    width: 70px;
  }

@layer utilities {
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-center-between {
    @apply flex items-center justify-between;
  }
}
