import React from 'react';
import { BasicTimer } from '@cloc/atoms';

export function ClocSmallTimer({
	border,
	background,
	color,
	rounded,
	className,
	...props
}: {
	border: 'none' | 'thick' | 'thin';
	background: 'destructive' | 'none' | 'primary' | 'secondary';
	color: 'destructive' | 'primary' | 'secondary';
	rounded: 'none' | 'small' | 'medium' | 'large';
	textAlign: string;
	className: string;
}) {
	return (
		<div>
			<BasicTimer {...props} className={className} background={background} border={border} color={color} />
		</div>
	);
}
