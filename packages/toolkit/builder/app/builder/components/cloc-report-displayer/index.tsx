import React from 'react';
import { ClocReportDisplayer, IReportDisplayer } from '@cloc/atoms';
import { Input as Inputs } from '@builder.io/sdk';

export function CardClocReportDisplayer(props: IReportDisplayer) {
	return <ClocReportDisplayer {...props} />;
}

export const InputCardClocReportDisplayer: Inputs[] = [
	{
		name: 'time',
		type: 'string'
	},
	{
		name: 'period',
		type: 'string'
	},
	{
		name: 'user',
		type: 'string',
		defaultValue: 'Salva'
	},
	{
		name: 'icon',
		type: 'ReactNode'
	}
];
