import React from 'react';
import { ClocButton } from '@cloc/atoms';
import { Input as Inputs } from '@builder.io/sdk';

interface IBasicClocButton {
	size: 'default' | 'sm' | 'lg';
}
export function BasicClocButton({ size }: IBasicClocButton) {
	return <ClocButton size={size == 'sm' ? 'sm' : 'default'} />;
}

export const InputClocButton: Inputs[] = [
	{
		name: 'size',
		type: 'enum',
		defaultValue: 'default',
		enum: [
			{ label: 'Default', value: 'default' },
			{ label: 'Small', value: 'sm' },
			{ label: 'Large', value: 'lg' }
		]
	}
];
