import React from 'react';
import { ClocMember } from '@cloc/atoms';
import { Input } from '@builder.io/sdk';

interface IMemberClocProps {
	size?: 'default' | 'sm' | 'lg';
	showProgress?: boolean;
	showTime?: boolean;
	className?: string;
}
export const BasicMember = ({ ...props }: IMemberClocProps) => {
	return <ClocMember {...props} />;
};

export const InputMember: Input[] = [
	{
		name: 'size',
		type: 'enum',
		defaultValue: 'default',
		enum: [
			{ label: 'Default', value: 'default' },
			{ label: 'Small', value: 'sm' },
			{ label: 'Large', value: 'lg' }
		]
	}
];
