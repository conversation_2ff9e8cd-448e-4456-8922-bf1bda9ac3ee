'use client';
import React from 'react';
import { ModernCloc as MCloc, useAccessToken } from '@cloc/atoms';
import { Input as Inputs } from '@builder.io/sdk';

export const ModernCloc = ({ separator, expanded, showProgress, draggable, resizable }: any) => {
	return (
		<div>
			<MCloc
				separator={separator}
				expanded={expanded}
				showProgress={showProgress}
				draggable={draggable}
				resizable={resizable}
			></MCloc>
		</div>
	);
};
export const InputModernCloc: Inputs[] = [
	{
		name: 'resizable',
		type: 'boolean',
		defaultValue: false
	},
	{
		name: 'showProgress',
		type: 'boolean',
		defaultValue: false
	},
	{
		name: 'expanded',
		type: 'boolean',
		defaultValue: false
	},
	{
		name: 'draggable',
		type: 'boolean',
		defaultValue: false
	}
];
