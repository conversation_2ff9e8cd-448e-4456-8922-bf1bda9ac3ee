import { useEditor, useNode } from '@craftjs/core';
import React, { useState } from 'react';
import { ModernCloc as MCloc } from '@cloc/atoms';
import { ActiveBorder } from '../../active-border';
import { EditBar } from '../../editbar';
import { modernClocEditbarConfig } from '../config/timer';
import { ModernClocProps } from '../constants/timer';

export const ModernCloc = ({
	separator,
	expanded,
	showProgress,
	draggable,
	resizable,
	...props
}: typeof ModernClocProps) => {
	const {
		id,
		connectors: { connect, drag }
	} = useNode();

	const { hoveredNodeId } = useEditor((state) => ({
		hoveredNodeId: state.events
	}));
	const hoveredId = hoveredNodeId ? Array.from(hoveredNodeId.hovered)[0] : null;

	return (
		<div style={{ alignItems: props.align }} className="flex w-full flex-col z-50 relative">
			<div
				ref={(ref) => {
					if (ref instanceof HTMLElement) {
						connect(drag(ref));
					}
				}}
				className="w-fit flex"
			>
				<ActiveBorder active={id == hoveredId} id={id}>
					<MCloc
						key={String(expanded)}
						className="w-fit"
						separator={separator}
						expanded={expanded}
						showProgress={showProgress}
						draggable={draggable}
						resizable={resizable}
					/>
				</ActiveBorder>
			</div>
		</div>
	);
};

export const ModernClocCompSettings = () => {
	return (
		<div className="space-y-4">
			<EditBar config={modernClocEditbarConfig} />
		</div>
	);
};

ModernCloc.craft = {
	props: ModernClocProps,
	related: {
		settings: ModernClocCompSettings
	}
};
