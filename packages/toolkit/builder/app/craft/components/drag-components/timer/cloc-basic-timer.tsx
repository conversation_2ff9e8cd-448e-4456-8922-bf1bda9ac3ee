import { useEditor, useNode } from '@craftjs/core';
import React from 'react';
import { ClocBasic } from '@cloc/atoms';
import { ActiveBorder } from '../../active-border';
import { EditBar } from '../../editbar';
import { clocBasicTimerEditbarConfig } from '../config/timer';
import { ClocBasicTimerProps } from '../constants/timer';

export const ClocBasicTimer = ({ ...props }: typeof ClocBasicTimerProps) => {
	const {
		id,
		connectors: { connect, drag }
	} = useNode();

	const { hoveredNodeId } = useEditor((state) => ({
		hoveredNodeId: state.events
	}));
	const hoveredId = hoveredNodeId ? Array.from(hoveredNodeId.hovered)[0] : null;
	return (
		<div style={{ alignItems: props.align }} className="flex flex-col w-full relative z-50">
			<div
				className="w-fit"
				ref={(ref) => {
					if (ref instanceof HTMLElement) {
						connect(drag(ref));
					}
				}}
			>
				<ActiveBorder active={id == hoveredId} id={id}>
					<ClocBasic {...props}></ClocBasic>
				</ActiveBorder>
			</div>
		</div>
	);
};

export const ClocBasicCompSettings = () => {
	return (
		<div>
			<EditBar config={clocBasicTimerEditbarConfig} />
		</div>
	);
};

ClocBasicTimer.craft = {
	props: ClocBasicTimerProps,
	related: {
		settings: ClocBasicCompSettings
	}
};
