import { useEditor, useNode } from '@craftjs/core';
import { ActiveBorder } from '../../active-border';
import { ClocChart } from '@cloc/atoms';
import { EditBar } from '../../editbar';
import { ConfigItem } from '../../../types';

interface IBasicClocReportProps {
	type?: 'bar' | 'bar-vertical' | 'area' | 'pie' | 'line' | 'radar' | 'radial' | 'tooltip';
	className?: string | undefined;
}
export const ClocChartComponent = ({ ...props }: IBasicClocReportProps) => {
	const {
		id,
		connectors: { connect, drag }
	} = useNode();
	const { hovedNodeId } = useEditor((state) => ({ hovedNodeId: state.events }));
	const hoveredId = hovedNodeId ? Array.from(hovedNodeId.hovered)[0] : null;

	return (
		<div
			ref={(ref) => {
				if (ref instanceof HTMLElement) {
					connect(drag(ref));
				}
			}}
		>
			<ActiveBorder active={id == hoveredId} id={id}>
				{<ClocChart type={props.type ?? 'line'}></ClocChart>}
			</ActiveBorder>
		</div>
	);
};

export const ClocBasicRapportSettings = () => {
	const editorConfig: ConfigItem[] = [
		{
			property: 'type',
			label: 'Chart Type',
			type: 'select',
			list: [
				{
					label: 'Bar',
					value: 'bar'
				},
				{
					label: 'Area',
					value: 'area'
				},
				{
					label: 'Pie',
					value: 'pie'
				},
				{
					label: 'Line',
					value: 'line'
				},
				{
					label: 'Radar',
					value: 'radar'
				},
				{
					label: 'Radial',
					value: 'radial'
				},
				{
					label: 'Tooltip',
					value: 'tooltip'
				}
			]
		}
	];

	return (
		<div>
			<EditBar config={editorConfig} />
		</div>
	);
};

export const ClocBasicRapportProps = {
	type: 'bar'
};

ClocChartComponent.craft = {
	props: ClocBasicRapportProps,
	related: {
		settings: ClocBasicRapportSettings
	}
};
