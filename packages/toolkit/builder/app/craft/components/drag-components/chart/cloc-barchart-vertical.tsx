import { Cloc<PERSON>hart } from '@cloc/atoms';
import { useEditor, useNode } from '@craftjs/core';
import { ActiveBorder } from '../../active-border';

export function BasicBarChartVertical() {
	const {
		id,
		connectors: { connect, drag }
	} = useNode();
	const { hoveredNodeId } = useEditor((state) => ({ hoveredNodeId: state.events }));
	const hoveredId = hoveredNodeId ? Array.from(hoveredNodeId.hovered)[0] : null;
	return (
		<div
			ref={(ref) => {
				if (ref instanceof HTMLElement) {
					connect(drag(ref));
				}
			}}
			draggable={false}
		>
			<ActiveBorder active={id == hoveredId} id={id}>
				<ClocChart type="bar-vertical" className="w-full" />
			</ActiveBorder>
		</div>
	);
}
