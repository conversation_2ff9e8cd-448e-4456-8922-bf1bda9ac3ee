import React from 'react';
import { ModernCloc } from '../../drag-components/timer/modern-cloc';
import { BaseTimer } from '../../drag-components/timer/base-timer';
import { ClocBasicTimer } from '../../drag-components/timer/cloc-basic-timer';
import { TimerButton } from '../../drag-components/timer/timer-button';
import { ModernClocProps, ClocBasicTimerProps, BaseTimerProps } from '../../drag-components';
import { ComponentDefinition } from '../../../types/component-types';

export const timerComponents: ComponentDefinition[] = [
    {
        label: "Modern Cloc Timer",
        id: "ModernClocTimer",
        component: <ModernCloc {...ModernClocProps} />,
        imageSrc: "/components/modern-cloc.png"
    },
    {
        label: "Cloc Timer Button",
        id: "ClocTimerButton",
        component: <TimerButton size="default" />,
        imageSrc: "/components/cloc-button.png"
    },
    {
        label: "Cloc Basic Timer",
        id: "ClocBasicTimer",
        component: <ClocBasicTimer {...ClocBasicTimerProps} />,
        imageSrc: "/components/basic-cloc.png"
    },
    {
        label: "Basic Timer",
        id: "BasicTimer",
        component: <BaseTimer {...BaseTimerProps} />,
        imageSrc: "/components/basic-timer.png"
    }
];
