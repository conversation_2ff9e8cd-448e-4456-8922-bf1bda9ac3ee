# Introduction

Craft.js is a powerful, React-based framework for building dynamic page editors. It enables developers to create complex, drag-and-drop interfaces with minimal effort. Here’s a short guide to get you started.

## Use-Case

Craft.js solves this problem by modularising the building blocks of a page editor. It provides a drag-n-drop system and handles the way user components should be rendered, updated and moved - among other things. With this, you'll be able to focus on building the page editor according to your own specifications and needs.

## Exception

We don't have any exception here.