import { Cloc<PERSON>rovider } from '@cloc/atoms';
import ReactDOM from 'react-dom/client';

import { renderToStaticMarkup } from 'react-dom/server';

export const renderReactComponent = ({ element }: { element: React.ReactNode }) => {
	return renderToStaticMarkup(<ClocProvider>{element}</ClocProvider>);
};

export const renderReactComponentHtml = ({ element }: { element: React.ReactNode }): Promise<string> => {
	const container = document.createElement('div');
	return new Promise((resolve) => {
		const root = ReactDOM.createRoot(container);
		root.render(<ClocProvider>{element}</ClocProvider>);
		setTimeout(() => {
			resolve(container.outerHTML);
		}, 100);
	});
};

export const renderReactComponentDynamic = (element: React.ReactElement, el: HTMLElement) => {
	if (!el.childNodes.length) {
		const root = ReactDOM.createRoot(el);
		root.render(<ClocProvider>{element}</ClocProvider>);
		return root;
	}
	return null;
};
