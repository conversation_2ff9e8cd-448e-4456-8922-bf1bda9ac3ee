import React, { forwardRef } from 'react';
import { ClocReportDisplayer } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { defaultTheme, ClocProvider } from '@cloc/atoms';
import { IReportDisplayer } from '../../types';

export const CardClocReportDisplayer = forwardRef<HTMLDivElement, IReportDisplayer>((props, ref) => {
  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocReportDisplayer {...props} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

CardClocReportDisplayer.displayName = 'CardClocReportDisplayer';
