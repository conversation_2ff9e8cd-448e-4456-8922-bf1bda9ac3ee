import React, { forwardRef } from 'react';
import { ClocProgressCircle, ProgressCircleProps } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const BasicProgressCircle = forwardRef<HTMLDivElement, ProgressCircleProps>((props, ref) => {
  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocProgressCircle {...props} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicProgressCircle.displayName = 'BasicProgressCircle'; 