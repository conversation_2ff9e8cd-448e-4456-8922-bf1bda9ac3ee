import React from 'react';
import { renderReactComponentDynamic } from '../../render';
import { ComponentView, ComponentModel } from '../../types';
import { Member } from '@cloc/types';
import { BasicClocMember } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const memberView: Partial<ComponentView> = {
  tagName: 'div',

  onRender({ el, model }: { el: HTMLElement; model: ComponentModel }) {
    if (!el) return;

    const container = document.createElement('div');
    container.className = 'basic-member-container';
    el.innerHTML = '';
    el.appendChild(container);

    const variant = model.get<'default' | 'bordered'>('variant');
    const size = model.get<'default' | 'sm' | 'lg'>('size') || 'default';

    const values = JSON.parse(model.get('values') || '[]') as Member[];

    const props = {
      title: model.get<string>('title') || 'Members Activities',
      showProgress: <PERSON><PERSON>an(model.get<boolean>('showProgress')),
      showTime: Boolean(model.get<boolean>('showTime')),
      size: size,
      variant: variant || 'default',
      values: values,
      className: `basic-member-wrapper ${variant === 'bordered' ? 'border-2' : ''}`
    };

    renderReactComponentDynamic(
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <BasicClocMember {...props} />
        </ClocProvider>
      </ThemeUIProvider>,
      container
    );
  }
};
