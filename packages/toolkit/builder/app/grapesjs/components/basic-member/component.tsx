import React, { forwardRef } from 'react';
import { BasicClocMember, IBasicClocMemberProps } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const BasicMember = forwardRef<HTMLDivElement, Partial<IBasicClocMemberProps>>((props, ref) => {
  const defaultProps: IBasicClocMemberProps = {
    title: props.title || 'Members Activities',
    showProgress: props.showProgress || false,
    showTime: props.showTime || false,
    size: props.size || 'default',
    variant: props.variant || 'default',
    values: props.values || [],
    classNameTitle: 'font-bold',
    className: `basic-member-wrapper ${props.variant === 'bordered' ? 'border-2' : ''}`
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <BasicClocMember {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicMember.displayName = 'BasicMember'; 