import { ComponentConfig } from '../../../types';

export const tooltipChartConfig: ComponentConfig = {
	type: 'basic-tooltipchart',
	label: 'Tooltip Chart',
	category: 'Chart',
	content: '<div data-gjs-type="basic-tooltipChart"></div>',
	image: '/img/chart.png',
	defaults: {
		tagName: 'div',
		attributes: {
			'data-component': 'ClocChart',
			'data-type': 'tooltip',
			style: 'min-height: 300px; width: 100%;'
		},
		draggable: '*',
		droppable: true,
		traits: []
	},
	mapping: {
		importPath: '@cloc/atoms',
		componentName: 'ClocChart',
		category: 'CHART',
		inputs: {
			name: 'InputCloc<PERSON><PERSON>',
			importPath: '@cloc/atoms'
		}
	}
};
