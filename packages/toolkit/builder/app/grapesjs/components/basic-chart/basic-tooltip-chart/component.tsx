import React, { forwardRef } from 'react';
import { Cloc<PERSON><PERSON> } from '@cloc/atoms';
import { IChartProps } from '@cloc/types';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';
import { ChartType } from '../../../types';

export const BasicTooltipChart = forwardRef<HTMLDivElement, Partial<IChartProps>>((props, ref) => {
  const defaultProps = {
    type: 'tooltip' as ChartType,
    data: props.data || []
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocChart {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicTooltipChart.displayName = 'BasicTooltipChart';
