import { ComponentConfig } from '../../../types';

export const lineChartConfig: ComponentConfig = {
	type: 'basic-linechart',
	label: 'Line Chart',
	category: 'Chart',
	content: '<div data-gjs-type="basic-linechart"></div>',
	image: '/img/line_chart.png',
	defaults: {
		tagName: 'div',
		attributes: {
			'data-component': 'ClocChart',
			'data-type': 'line',
			style: 'min-height: 300px; width: 100%;'
		},
		draggable: '*',
		droppable: true,
		traits: []
	},
	mapping: {
		importPath: '@cloc/atoms',
		componentName: 'ClocChart',
		category: 'CHART',
		inputs: {
			name: 'InputCloc<PERSON>hart',
			importPath: '@cloc/atoms'
		}
	}
};
