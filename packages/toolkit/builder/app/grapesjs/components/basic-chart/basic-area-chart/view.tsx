import React from 'react';
import { renderReactComponentDynamic } from '../../../render';
import { Cloc<PERSON>hart } from '@cloc/atoms';
import { ComponentView, ComponentModel } from '../../../types';

export const areaChartView: Partial<ComponentView> = {
  tagName: 'div',

  onRender({ el, model }: { el: HTMLElement; model: ComponentModel }) {
    if (!el) return;

    const container = document.createElement('div');
    container.className = 'area-chart-container';
    el.innerHTML = '';
    el.appendChild(container);

    // Add data attributes for export parsing
    el.setAttribute('data-component', 'ClocChart');
    el.setAttribute('data-type', 'area');

    const props = {
      type: 'area' as const,
      data: model.get('data') || [],
      className: 'area-chart-wrapper'
    };

    renderReactComponentDynamic(
      <ClocChart {...props} />,
      container
    );
  },

  listenToEvents(el: HTMLElement) {
    if (!el) {
      console.error('Element not found for event binding');
      return;
    }
    el.addEventListener('dragend', () => {
      console.log('Drag ended');
    });
  }
};
