import { ComponentConfig } from '../../../types';

export const areaChartConfig: ComponentConfig = {
	type: 'basic-areachart',
	label: 'Area Chart',
	category: 'Chart',
	content: '<div data-gjs-type="basic-areachart"></div>',
	image: '/img/area_chart.png',
	defaults: {
		tagName: 'div',
		attributes: {
			'data-component': 'ClocChart',
			'data-type': 'area',
			style: 'min-height: 300px; width: 100%;'
		},
		draggable: '*',
		droppable: true,
		traits: []
	},
	mapping: {
		importPath: '@cloc/atoms',
		componentName: 'ClocChart',
		category: 'CHART',
		inputs: {
			name: 'InputCloc<PERSON>hart',
			importPath: '@cloc/atoms'
		}
	}
};
