import React from 'react';
import { renderReactComponentDynamic } from '../../../render';
import { Cloc<PERSON><PERSON> } from '@cloc/atoms';
import { ComponentView, ComponentModel } from '../../../types';

export const barChartVerticalView: Partial<ComponentView> = {
    tagName: 'div',

    onRender({ el, model }: { el: HTMLElement; model: ComponentModel }) {
        if (!el) return;

        const container = document.createElement('div');
        container.className = 'bar-vertical-chart-container';
        el.innerHTML = '';
        el.appendChild(container);

        // Add data attributes for export parsing
        el.setAttribute('data-component', 'ClocChart');
        el.setAttribute('data-type', 'bar-vertical');

        const props = {
            type: 'bar-vertical' as const,
            data: model.get('data') || [],
            className: 'bar-vertical-chart-wrapper'
        };

        renderReactComponentDynamic(
            <ClocChart {...props} />,
            container
        );
    },

    listenToEvents(el: HTMLElement) {
        if (!el) {
            console.error('Element not found for event binding');
            return;
        }
        el.addEventListener('dragend', () => {
            console.log('Drag ended');
        });
    }
};
