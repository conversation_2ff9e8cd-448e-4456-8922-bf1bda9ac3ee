import { ComponentConfig } from '../../../types';

export const barChartVerticalConfig: ComponentConfig = {
	type: 'basic-chart-vertical',
	label: 'Vertical Bar Chart',
	category: 'Chart',
	content: '<div data-gjs-type="basic-chart-vertical"></div>',
	image: '/img/chart.png',
	defaults: {
		tagName: 'div',
		attributes: {
			'data-component': 'ClocChart',
			'data-type': 'bar-vertical',
			style: 'min-height: 300px; width: 100%;'
		},
		draggable: '*',
		droppable: true,
		traits: []
	},
	mapping: {
		importPath: '@cloc/atoms',
		componentName: 'ClocChart',
		category: 'CHART',
		inputs: {
			name: 'InputCloc<PERSON><PERSON>',
			importPath: '@cloc/atoms'
		}
	}
};
