import React, { forwardRef } from 'react';
import { ClocDateRangePicker } from '@cloc/atoms';
import { IDateRangePickerProps } from '@cloc/types';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const BasicDateRanger = forwardRef<HTMLDivElement, Partial<IDateRangePickerProps>>((props, ref) => {
  const defaultProps: IDateRangePickerProps = {
    className: `date-ranger-wrapper`
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocDateRangePicker {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicDateRanger.displayName = 'BasicDateRanger'; 