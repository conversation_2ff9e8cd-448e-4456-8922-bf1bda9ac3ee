import React, { forwardRef } from 'react';
import { ClocDatePicker } from '@cloc/atoms';
import { IDatePickerProps } from '@cloc/types';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const BasicDatePicker = forwardRef<HTMLDivElement, Partial<IDatePickerProps>>((props, ref) => {
  // Ensure we're properly handling boolean values
  const icon = typeof props.icon === 'boolean' ? props.icon : true;
  const placeholder = props.placeholder || 'Pick a date';
  const className = props.className || '';

  const defaultProps: IDatePickerProps = {
    icon,
    placeholder,
    className: `date-picker-wrapper ${className}`.trim()
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocDatePicker {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicDatePicker.displayName = 'BasicDatePicker'; 