import { ComponentTrait, ComponentConfig } from '../../../types';

const traits: ComponentTrait[] = [
	{
		type: 'select',
		label: '<PERSON><PERSON>',
		name: 'size',
		changeProp: 1,
		default: 'default',
		options: [
			{ value: 'default', name: 'Default' },
			{ value: 'sm', name: 'Small' },
			{ value: 'lg', name: 'Large' }
		]
	},
	{
		type: 'select',
		label: 'Variant',
		name: 'variant',
		changeProp: 1,
		default: 'default',
		options: [
			{ value: 'default', name: 'Default' },
			{ value: 'bordered', name: 'Bordered' }
		]
	}
];

export const clocTimerButtonConfig: ComponentConfig = {
	type: 'data-timer-button',
	label: 'Timer Button',
	category: 'Timer',
	content: '<div data-gjs-type="data-timer-button"></div>',
	defaults: {
		tagName: 'div',
		attributes: {
			'data-component': 'ClocTimerButton',
			'data-size': 'default',
			'data-variant': 'default'
		},
		draggable: '*',
		droppable: true,
		traits
	},
	mapping: {
		importPath: '@cloc/atoms',
		componentName: 'ClocButton',
		category: 'TIMER',
		inputs: {
			name: 'InputClocTimerButton',
			importPath: '@cloc/atoms'
		}
	}
};
