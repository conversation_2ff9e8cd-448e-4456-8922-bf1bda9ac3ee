import React, { forwardRef } from 'react';
import { ClocButton } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';
import { IClocTimerButtonProps } from '../../../types';

export const ClocTimerButton = forwardRef<HTMLDivElement, Partial<IClocTimerButtonProps>>((props, ref) => {
  const defaultProps = {
    size: props.size || 'default',
    variant: props.variant || 'default',
    style: props.style,
    className: `cloc-timer-button ${props.variant === 'bordered' ? 'border-2' : ''}`
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocButton {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

ClocTimerButton.displayName = 'ClocTimerButton';
