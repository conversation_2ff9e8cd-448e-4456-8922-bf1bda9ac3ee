import React from 'react';
import { renderReactComponentDynamic } from '../../../render';
import { ClocButton } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';
import { ComponentView, ComponentModel } from '../../../types';

export const clocTimerButtonView: Partial<ComponentView> = {
  tagName: 'div',

  onRender({ el, model }: { el: HTMLElement; model: ComponentModel }) {
    if (!el) return;

    const container = document.createElement('div');
    container.className = 'cloc-timer-button-container';
    el.innerHTML = '';
    el.appendChild(container);

    // Add data attributes for export parsing
    el.setAttribute('data-component', 'ClocButton');
    el.setAttribute('data-size', model.get('size') || 'default');
    el.setAttribute('data-variant', model.get('variant') || 'default');

    const props = {
      size: model.get<'default' | 'sm' | 'lg'>('size') || 'default',
      variant: model.get<'default' | 'bordered'>('variant') || 'default',
      className: `cloc-timer-button ${model.get('variant') === 'bordered' ? 'border-2' : ''}`
    };

    renderReactComponentDynamic(
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocButton {...props} />
        </ClocProvider>
      </ThemeUIProvider>,
      container
    );
  }
};
