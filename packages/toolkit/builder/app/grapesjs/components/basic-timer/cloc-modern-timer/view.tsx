import React from 'react';
import { renderReactComponentDynamic } from '../../../render';
import { ModernClocWrapper } from './component';
import { ComponentView, ComponentModel } from '../../../types';

export const modernTimerView: Partial<ComponentView> = {
  tagName: 'div',

  onRender({ el, model }: { el: HTMLElement; model: ComponentModel }) {
    if (!el) return;

    const container = document.createElement('div');
    container.className = 'modern-cloc-container';
    el.innerHTML = '';
    el.appendChild(container);

    const variant = model.get<'default' | 'bordered'>('variant');
    const size = model.get<'default' | 'sm'>('size') || 'default';
    const expanded = Boolean(model.get<boolean>('expanded'));
    const showProgress = Boolean(model.get<boolean>('showProgress'));
    const resizable = Boolean(model.get<boolean>('resizable'));
    
    // Add data attributes for export parsing
    el.setAttribute('data-component', 'ModernCloc');
    el.setAttribute('data-variant', variant || 'default');
    el.setAttribute('data-size', size);
    el.setAttribute('data-expanded', String(expanded));
    el.setAttribute('data-show-progress', String(showProgress));
    if (resizable) {
      el.setAttribute('data-resizable', 'true');
    }
    
    const props = {
      readonly: Boolean(model.get<boolean>('readonly')),
      separator: model.get<string>('separator') || ':',
      expanded,
      draggable: Boolean(model.get<boolean>('draggable')),
      resizable,
      showProgress,
      size,
      variant: variant === 'bordered' ? null : ('default' as const),
      className: `modern-cloc-wrapper ${variant === 'bordered' ? 'border-2' : ''}`
    };

    renderReactComponentDynamic(
      <ModernClocWrapper {...props} />,
      container
    );
  }
}; 