import React, { forwardRef } from 'react';
import { ModernCloc } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';
import { IModernClocProps } from '../../../types';

export const ModernClocWrapper = forwardRef<HTMLDivElement, Partial<IModernClocProps>>((props, ref) => {
  const defaultProps: IModernClocProps = {
    separator: props.separator || ':',
    expanded: props.expanded || false,
    showProgress: props.showProgress || false,
    draggable: props.draggable || false,
    resizable: props.resizable || false,
    size: props.size || 'default',
    variant: props.variant || 'default',
    className: `modern-cloc-wrapper ${props.variant === null ? 'border-2' : ''}`
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ModernCloc {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

ModernClocWrapper.displayName = 'ModernClocWrapper'; 