import React, { forwardRef } from 'react';
import { ClocBasic } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';
import { IClocBasicProps } from '../../../types';

export const ClocBasicWrapper = forwardRef<HTMLDivElement, Partial<IClocBasicProps>>((props, ref) => {
  const defaultProps: IClocBasicProps = {
    readonly: props.readonly || false,
    progress: props.progress || false,
    background: props.background || 'primary',
    rounded: props.rounded || 'large',
    color: props.color || 'secondary',
    icon: props.icon || false,
    border: props.border || 'thick',
    className: 'basic-cloc-wrapper'
  };

  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <ClocBasic {...defaultProps} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

ClocBasicWrapper.displayName = 'ClocBasicWrapper';
