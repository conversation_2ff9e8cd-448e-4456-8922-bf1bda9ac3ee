import React, { forwardRef } from 'react';
import { BasicTimer as BaseTimer } from '@cloc/atoms';
import { ClocBasicProps } from '@cloc/atoms';
import { ThemeUIProvider } from 'theme-ui';
import { ClocProvider, defaultTheme } from '@cloc/atoms';

export const BasicTimer = forwardRef<HTMLDivElement, ClocBasicProps>((props, ref) => {
  return (
    <div ref={ref}>
      <ThemeUIProvider theme={defaultTheme}>
        <ClocProvider>
          <BaseTimer {...props} />
        </ClocProvider>
      </ThemeUIProvider>
    </div>
  );
});

BasicTimer.displayName = 'BasicTimer'; 