export const builders = [
    {
      name: 'Builder.io',
      href: '/framework/builder-io',
      icon: '/builder.io.png',
      features: [
        {
          main: 'Requires registration and API Keys'
        },
        {
          main: 'Two embedding options',
          sub: [
            'Copy and paste the code',
            'Publish changes for automatic updates'
          ]
        }
      ],
      tags: ['Free up to 10k visual/page views', 'Paid for high traffic']
    },
    {
      name: 'GrapesJS',
      href: '/framework/grapesjs',
      icon: '/grapesJS.png',
      features: [
        {
          main: 'No registration or tokens required'
        },
        {
          main: 'Code can be exported for manual embedding'
        },
        {
          main: 'Focused on basic HTML and CSS elements'
        }
      ],
      tags: ['Free', 'Open Source', 'Limited ReactJS Support']
    },
    {
      name: 'Plasmic',
      href: '/framework/plasmic',
      icon: '/plasmic.png',
      features: [
        {
          main: 'Requires registration and tokens (API Key and Secret Key)'
        },
        {
          main: 'Offers drag-and-drop customization within its interface'
        }
      ],
      tags: ['Free Basic Configuration']
    },
    {
      name: 'Craft.js',
      href: '/framework/craftjs',
      icon: '/craft.js.svg',
      features: [
        {
          main: 'Does not require tokens but involves custom development'
        },
        {
          main: 'React / NextJS support with inline styles & TailwindCSS'
        },
        {
          main: 'Allows drag-and-drop component building'
        },
        {
          main: 'Cloc provides wrapper functionality'
        }
      ],
      tags: ['Free', 'Copy Code functionality not included']
    }
  ];