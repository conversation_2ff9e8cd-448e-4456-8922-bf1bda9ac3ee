export const ComponentUsage = () => (
  <div className="mb-32">
    <h2 className="text-4xl font-bold mb-8 text-slate-900 dark:text-slate-100">Available Components</h2>
    
    <div className="space-y-12">
      <div>
        <h3 className="text-2xl font-semibold mb-6 text-slate-800 dark:text-slate-200">Timer Components</h3>
        
        <div className="space-y-8">
          <div>
            <h4 className="text-lg font-semibold mb-3 text-slate-700 dark:text-slate-300">Modern Timer</h4>
            <ul className="list-disc pl-6 space-y-2 text-slate-600 dark:text-slate-400">
              <li>Advanced countdown with progress tracking</li>
              <li>Customizable appearance</li>
              <li>Progress visualization</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-3 text-slate-700 dark:text-slate-300">Basic Timer</h4>
            <ul className="list-disc pl-6 space-y-2 text-slate-600 dark:text-slate-400">
              <li>Simple countdown display</li>
              <li>Clean, minimal design</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-3 text-slate-700 dark:text-slate-300">Progress Circle</h4>
            <ul className="list-disc pl-6 space-y-2 text-slate-600 dark:text-slate-400">
              <li>Visual time tracking</li>
              <li>Customizable colors and size</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-3 text-slate-700 dark:text-slate-300">Analytics Charts</h4>
            <ul className="list-disc pl-6 space-y-2 text-slate-600 dark:text-slate-400">
              <li>Time tracking visualizations</li>
              <li>Performance metrics</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
);