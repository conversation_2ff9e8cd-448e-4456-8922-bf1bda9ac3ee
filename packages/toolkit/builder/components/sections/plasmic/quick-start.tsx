import { CodeBlock } from '../../ui/CodeBlock';

export const QuickStart = () => (
	<div className="mb-32">
		<h2 className="text-4xl font-bold mb-8 text-slate-900 dark:text-slate-100">Quick Start</h2>
		<div className="space-y-8">
			<div>
				<h3 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-200">
					1. Install Dependencies
				</h3>
				<p className="text-slate-600 dark:text-slate-400 mb-4">
					First, install the required packages. The @cloc/atoms package contains our core components, UI and
					Theming.
				</p>
				<CodeBlock language="bash" code="npm install @cloc/atoms" />
			</div>

			<div>
				<h3 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-200">
					2. Setup Theme Provider
				</h3>
				<p className="text-slate-600 dark:text-slate-400 mb-4">
					Wrap your application with ClocProvider to enable theming and context features.
				</p>
				<CodeBlock
					language="typescript"
					code={`import { ClocProvider } from '@cloc/atoms';

export default function App() {
  return (
    <ClocProvider>
      {/* Your app content */}
    </ClocProvider>
  );
}`}
				/>
			</div>

			<div>
				<h3 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-200">
					3. Register Components in Plasmic
				</h3>
				<p className="text-slate-600 dark:text-slate-400 mb-4">
					Register the components you want to use in Plasmic. This makes them available in the Plasmic Studio
					interface.
				</p>
				<CodeBlock
					language="typescript"
					code={`import { ClocLoginForm, ModernTimer, ClocProgress } from '@cloc/atoms';
import { PLASMIC } from './plasmic-init';

// Register Authentication Form
PLASMIC.registerComponent(ClocLoginForm, {
  name: 'Cloc Login Form',
  props: {
    source: {
      type: 'choice',
      options: [
        { label: 'Storybook', value: 'storybook' },
        { label: 'Example', value: 'example' }
      ]
    },
    className: { type: 'string' }
  }
});

// Register Modern Timer
PLASMIC.registerComponent(ModernTimer, {
  name: 'Modern Timer',
  props: {
    variant: {
      type: 'choice',
      options: [
        { label: 'Basic', value: 'basic' },
        { label: 'Progress', value: 'progress' },
        { label: 'Expanded', value: 'expanded' }
      ]
    },
    showControls: { type: 'boolean', defaultValue: true }
  }
});`}
				/>
			</div>

			<div>
				<h3 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-200">
					4. Configure Authentication (Optional)
				</h3>
				<p className="text-slate-600 dark:text-slate-400 mb-4">
					If you&apos;re using authentication features, configure your auth settings.
				</p>
				<CodeBlock
					language="typescript"
					code={`import { ClocConfig } from '@cloc/atoms';

ClocConfig.init({
  authEndpoint: 'https://your-auth-endpoint.com',
  apiKey: process.env.NEXT_PUBLIC_CLOC_API_KEY
});`}
				/>
			</div>
		</div>
	</div>
);
