import { PropsWithChildren } from 'react';
import { cn } from '@cloc/ui';
import { SpinnerLoader } from './loader';
import { ArrowLeftIcon } from 'lucide-react';

type Props = {
	variant?:
		| 'primary'
		| 'outline'
		| 'outline-dark'
		| 'outline-danger'
		| 'ghost'
		| 'light'
		| 'dark'
		| 'grey'
		| 'danger';
	loading?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

/**
 * `export function Button({ children, className, variant = 'primary', loading, ...rest }: Props)`
 *
 * The above function is a React component that takes in the following props:
 *
 * - `children`: The content of the button.
 * - `className`: A class name that can be used to override the default styles.
 * - `variant`: The variant of the button. The default value is `primary`.
 * - `loading`: A boolean value that determines whether the button is loading or not.
 * - `...rest`: Any other props that are not listed above
 * @param {Props}  - `children` - The content of the button.
 * @returns A button with a spinner loader inside of it.
 */
export function Button({ children, className, variant = 'primary', loading, ...rest }: Props) {
	return (
		<button
			className={cn(
				'flex flex-row items-center justify-center py-3 px-4 gap-3 rounded-md min-w-[140px]',
				[
					variant === 'primary' && [
						'bg-primary dark:bg-primary-light text-white text-sm',
						'disabled:bg-primary-light disabled:opacity-40'
						// 'disabled:bg-primary-light dark:disabled:bg-[#33353E] disabled:opacity-40 dark:disabled:opacity-50',
					],
					variant === 'outline' && [
						'text-primary border border-primary font-medium',
						'dark:text-white border dark:border-white',
						'disabled:opacity-40'
					],
					variant === 'outline-dark' && ['input-border font-medium', 'disabled:opacity-40'],
					variant === 'grey' && [
						'disabled:opacity-40',
						'bg-light--theme-dark',
						'dark:bg-light--theme-dark',
						'dark:text-primary'
					],
					variant === 'danger' && [
						'disabled:opacity-40 bg-[#EB6961] text-white dark:bg-[#EB6961] text-base font-semibold'
					],
					variant === 'outline-danger' && [
						'text-[#EB6961] border border-[#EB6961] font-medium',
						'disabled:opacity-40'
					]
				],
				className
			)}
			{...rest}
		>
			{loading && <SpinnerLoader size={17} variant={variant === 'outline' ? 'primary' : 'light'} />}
			{children}
		</button>
	);
}

type RoundedButtonProps = PropsWithChildren & React.ButtonHTMLAttributes<HTMLButtonElement>;

export function RoundedButton({ children, className, ...rest }: RoundedButtonProps) {
	return (
		<button
			className={cn(
				'bg-white dark:bg-dark-lighter dark:text-white rounded-full',
				'shadow-[0px_4px_24px_rgba(0,0,0,0.25)] dark:shadow-darker',
				'flex justify-center items-center text-default',
				className
			)}
			{...rest}
		>
			{children}
		</button>
	);
}

export function BackButton({ onClick, className }: { onClick?: () => void; className?: string }) {
	return (
		<button
			type="button"
			onClick={onClick}
			onKeyDown={(e) => e.key === 'Enter' && onClick?.()}
			aria-label="Go back"
			className={cn('flex items-center justify-start text-sm', className)}
		>
			<ArrowLeftIcon className="w-full max-w-[20px]" />
			<span className="text-sm">BACK</span>
		</button>
	);
}
