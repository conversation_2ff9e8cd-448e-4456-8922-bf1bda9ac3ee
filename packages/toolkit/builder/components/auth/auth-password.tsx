'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useAuthForm } from '@cloc/atoms';
import { AuthLayout } from './auth-layout';
import { LoginForm } from './login-form';
import { LoginTokenForm } from './login-token-form';
import { LoadingScreen } from './loading-screen';
import { cn } from '@cloc/ui';

const NAVIGATION_DELAY = 800;

export function AuthPassword() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const returnUrl = searchParams.get('returnUrl') || '/';
	const [isNavigating, setIsNavigating] = useState(false);

	const form = useAuthForm({
		navigateFunc: handleNavigation,
		type_screen: 'screen-builder'
	});

	function handleNavigation() {
		setIsNavigating(true);
		setTimeout(() => {
			router.push(returnUrl);
		}, NAVIGATION_DELAY);
	}

	if (isNavigating) {
		return <LoadingScreen />;
	}

	return (
		<AuthLayout
			title="Login to the Cloc Kit Builder"
			description="Please enter your login information."
		>
			<div className="w-[98%] md:w-[550px] overflow-x-hidden">
				<div className={cn('flex flex-row transition-[transform] duration-500')}>
					{form.authScreen.screen === 'login' && <LoginForm form={form} />}
					{form.authScreen.screen === 'token' && <LoginTokenForm form={form} />}
				</div>
			</div>
		</AuthLayout>
	);
}
