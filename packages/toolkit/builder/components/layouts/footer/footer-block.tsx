'use client';;
import Link from 'next/link';
import { FC, SVGProps } from 'react';

export const EverIcon: FC<SVGProps<SVGSVGElement>> = (props) => (
	<svg width={199} height={41} viewBox="0 0 199 41" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
		<path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M0.581185 28.8938L0.301305 29.5465L1.00836 29.6933L9.83811 31.5263L9.84483 31.5383L14.1245 39.964L14.4535 40.6118L15.0316 40.1614L22.0143 34.7217L31.2344 36.5463L31.9289 36.6839L31.9502 35.9899L32.243 26.4022L32.259 26.3892V26.3856L39.4223 20.49L40 20.0144L39.3953 19.5724L31.9353 14.1194L32.336 4.51961L32.3695 3.71936L31.5808 3.93097L22.1152 6.47087L14.0859 0.816162L13.4927 0.398438L13.1922 1.05067L9.19627 9.72207L0.71239 11.9895L0.0104603 12.177L0.334577 12.8151L4.20144 20.4291L4.20635 20.4387L0.581185 28.8938ZM4.90946 21.823L1.96323 28.6944L9.15843 30.1881L4.90946 21.823ZM10.6745 30.5431L10.6516 30.498L5.52582 20.4076L10.05 10.713L21.8593 7.74947L30.7182 14.6122L30.7151 14.6886L30.7375 14.705L31.048 25.8507L21.7022 33.4236L10.6745 30.5431ZM11.3843 31.9406L14.8657 38.7944L20.5803 34.3426L11.3843 31.9406ZM23.1683 33.7554L30.7765 35.2611L31.0167 27.3958L23.1683 33.7554ZM32.2172 24.8906L38.0756 20.0688L31.9583 15.5974L32.2172 24.8906ZM30.7787 13.166L31.1081 5.27198L23.2948 7.36853L30.7787 13.166ZM20.6111 6.85386L13.9812 2.18471L10.6813 9.34573L20.6111 6.85386ZM8.55107 11.1083L1.72897 12.9316L4.84103 19.0585L8.55107 11.1083ZM33.1326 6.4147L38.8031 17.5786L38.4651 17.7433L32.7945 6.57944L33.1326 6.4147ZM29.1717 3.68892L15.8809 1.04316L15.8057 1.40557L29.0965 4.05131L29.1717 3.68892ZM11.1714 2.14916L2.31222 10.1652L2.05606 9.89356L10.9153 1.8775L11.1714 2.14916ZM0.377393 26.7212L0.677161 14.6517L0.29976 14.6428L0 26.7121L0.377393 26.7212ZM2.54456 30.8432L12.6515 38.6352L12.4181 38.9257L2.3111 31.1338L2.54456 30.8432ZM17.4202 40.3295L29.3893 37.6062L29.3039 37.2458L17.3348 39.9693L17.4202 40.3295ZM38.7593 22.4969L32.8416 34.4362L32.5019 34.2746L38.4196 22.3353L38.7593 22.4969ZM138.752 36.9151C136.572 36.9465 134.404 36.5852 132.352 35.8484C130.636 35.2206 129.08 34.2186 127.799 32.9151C125.322 30.1938 124.019 26.6051 124.172 22.9284C124.103 17.8855 126.024 13.0185 129.52 9.38309C131.312 7.59191 133.446 6.17902 135.795 5.22826C138.143 4.27751 140.659 3.80828 143.192 3.84842C146.516 3.67235 149.782 4.7689 152.327 6.91509C153.356 7.9154 154.165 9.12048 154.699 10.4525C155.234 11.7845 155.484 13.2139 155.432 14.6484C155.357 17.4903 154.703 20.2871 153.512 22.8684H134.846C134.812 23.51 134.91 24.1516 135.133 24.7542C135.355 25.3568 135.698 25.9078 136.14 26.3738C136.784 26.9534 137.537 27.3984 138.356 27.6825C139.174 27.9667 140.041 28.0843 140.906 28.0284C143.912 27.8319 146.821 26.8828 149.366 25.2684L152.366 33.1884C148.267 35.6824 143.55 36.9738 138.752 36.9151ZM142.652 12.0684C141.219 12.0325 139.812 12.4557 138.637 13.276C137.462 14.0964 136.579 15.271 136.119 16.6284H146.252C146.433 16.1274 146.535 15.6009 146.552 15.0684C146.552 13.2178 145.058 12.0684 142.652 12.0684ZM64.5922 36.9151C62.4121 36.9465 60.2442 36.5852 58.1922 35.8484C56.4756 35.2206 54.9202 34.2186 53.6389 32.9151C51.1618 30.1938 49.8585 26.6051 50.0122 22.9284C49.9429 17.8855 51.8645 13.0185 55.3602 9.38309C57.1523 7.59191 59.2859 6.17902 61.6345 5.22826C63.9831 4.27751 66.4988 3.80828 69.0322 3.84842C72.3562 3.67235 75.6225 4.7689 78.1669 6.91509C79.1963 7.9154 80.0046 9.12048 80.5395 10.4525C81.0743 11.7845 81.324 13.2139 81.2722 14.6484C81.1967 17.4903 80.5434 20.2871 79.3522 22.8684H60.6922C60.659 23.51 60.7566 24.1516 60.9792 24.7542C61.2018 25.3568 61.5446 25.9078 61.9869 26.3738C62.6307 26.9534 63.384 27.3984 64.2024 27.6825C65.0207 27.9667 65.8877 28.0843 66.7522 28.0284C69.7589 27.8319 72.668 26.8828 75.2122 25.2684L78.2122 33.1884C74.1116 35.6836 69.3919 36.975 64.5922 36.9151ZM68.4922 12.0684C67.0582 12.0311 65.6498 12.4536 64.4731 13.274C63.2964 14.0945 62.413 15.2699 61.9522 16.6284H72.0922C72.2735 16.1274 72.3747 15.6009 72.3922 15.0684C72.3922 13.2178 70.8975 12.0684 68.4922 12.0684ZM101.012 36.6684C96.0415 36.6684 92.4495 35.3924 90.3362 32.8764C88.3362 30.5004 87.7135 27.0938 88.4695 22.7431L89.5495 16.4431C89.7842 15.2698 89.7429 14.5698 89.4162 14.1764C89.2972 14.0462 89.1497 13.9453 88.9853 13.8814C88.8208 13.8176 88.6439 13.7926 88.4682 13.8084C87.4557 13.8267 86.4576 14.0512 85.5349 14.4684L86.7949 5.34842C89.1806 4.49693 91.7024 4.0902 94.2349 4.14842C97.0122 4.14842 99.0269 4.87376 100.224 6.30576C101.538 7.87642 101.904 10.3404 101.315 13.6284L99.9949 20.9484C99.6562 23.0938 99.8362 24.5404 100.544 25.3698C100.802 25.646 101.118 25.8612 101.469 25.9997C101.82 26.1381 102.198 26.1962 102.575 26.1698C103.399 26.2089 104.217 26.0159 104.937 25.6129C105.657 25.2098 106.25 24.6128 106.647 23.8898C107.662 22.1911 108.155 19.5618 108.155 15.8498C108.092 12.0794 107.568 8.33096 106.592 4.68842H118.532C119.095 8.01982 119.395 11.3901 119.432 14.7684C119.432 28.8911 112.891 36.6684 101.012 36.6684ZM172.346 36.0684H161.132L164.552 16.4484C164.79 15.6892 164.731 14.868 164.386 14.1511C164.269 14.03 164.128 13.9364 163.971 13.8771C163.814 13.8179 163.646 13.7944 163.479 13.8084C162.45 13.866 161.437 14.0888 160.479 14.4684L161.799 5.34842C164.215 4.5366 166.75 4.1311 169.299 4.14842C170.889 4.08127 172.468 4.44668 173.867 5.20576C174.487 5.58291 175.024 6.08279 175.445 6.67477C175.865 7.26674 176.16 7.93835 176.312 8.64842C177.125 7.2595 178.292 6.11116 179.694 5.3206C181.096 4.53004 182.683 4.12556 184.292 4.14842C186.129 4.12436 187.944 4.55734 189.572 5.40842L186.392 15.9684C184.608 15.133 182.662 14.7027 180.692 14.7084C177.05 14.7084 175.708 17.8284 175.226 19.6884L172.346 36.0671V36.0684ZM184.68 30.5143C184.68 29.1958 185.071 27.9069 185.803 26.8105C186.536 25.7142 187.577 24.8597 188.795 24.3551C190.013 23.8505 191.354 23.7185 192.647 23.9758C193.94 24.233 195.128 24.8679 196.06 25.8003C196.993 26.7326 197.628 27.9205 197.885 29.2137C198.142 30.5069 198.01 31.8474 197.506 33.0656C197.001 34.2837 196.146 35.3249 195.05 36.0575C193.954 36.79 192.665 37.181 191.346 37.181C189.579 37.1789 187.884 36.4758 186.635 35.226C185.385 33.9762 184.682 32.2818 184.68 30.5143ZM185.569 30.5143C185.569 31.657 185.908 32.774 186.543 33.724C187.178 34.6741 188.08 35.4146 189.135 35.8519C190.191 36.2892 191.353 36.4036 192.473 36.1806C193.594 35.9577 194.624 35.4075 195.432 34.5995C196.24 33.7915 196.79 32.7621 197.013 31.6414C197.236 30.5207 197.121 29.3591 196.684 28.3034C196.247 27.2478 195.506 26.3455 194.556 25.7106C193.606 25.0758 192.489 24.737 191.346 24.737C189.815 24.7388 188.346 25.348 187.263 26.4311C186.18 27.5142 185.571 28.9826 185.569 30.5143ZM189.213 33.8477H188.297L189.532 26.7383H191.665C192.288 26.7008 192.901 26.9074 193.374 27.3143C193.571 27.5126 193.718 27.7544 193.804 28.0201C193.889 28.2858 193.911 28.5679 193.868 28.8437C193.835 29.2776 193.665 29.6898 193.381 30.0197C193.071 30.3712 192.675 30.6355 192.232 30.7863L193.334 33.7903V33.8517H192.354L191.358 30.9717H189.717L189.213 33.8477ZM189.844 30.205H191.192C191.618 30.2173 192.036 30.0858 192.378 29.8317C192.535 29.7126 192.666 29.5633 192.763 29.3927C192.861 29.222 192.923 29.0335 192.946 28.8383C192.979 28.6705 192.974 28.4977 192.931 28.3321C192.889 28.1666 192.81 28.0125 192.701 27.881C192.573 27.7578 192.421 27.6618 192.255 27.599C192.089 27.5362 191.912 27.5079 191.734 27.5157H190.313L189.844 30.205Z"
			fill="currentColor"
		/>
	</svg>
);

interface CompanyInfo {
	name: string;
	logo: FC<SVGProps<SVGSVGElement>>;
	copyright: string;
	legalLinks?: Array<{
		text: string;
		href: string;
	}>;
	disclaimer?: string;
}

export const companyInfo: CompanyInfo = {
	name: "Ever Co. LTD",
	logo: EverIcon,
	copyright: "Copyright © 2024-present Ever Co. LTD. All Rights Reserved",
	legalLinks: [],
	disclaimer: "*All product names, logos, and brands are property of their respective owners. All company, product and service names used in this website are for identification purposes only. Use of these names, logos, and brands does not imply endorsement."
};

import { SystemStatus, SystemStatusProps } from './system-status';
import { ThemeToggle } from './theme-toggle';

export interface ThemeToggleProps {
	isDark: boolean;
	onToggle: () => void;
}

export interface FooterProps {
	systemStatus?: SystemStatusProps;
}

const FooterBlock: FC<FooterProps> = ({ systemStatus }) => {
	return (
		<footer className="flex flex-col bg-white dark:bg-transparent items-center mt-auto">
			<div className="py-8 w-full bg-[#F6F7FA] dark:bg-stone-950">
				<div className="container mx-auto px-4">
					<div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
						{/* Logo Section */}
						<div className="lg:col-span-3 flex items-center">
							{companyInfo.logo && (
								<companyInfo.logo className="w-48 h-auto text-gray-600 dark:text-[#565656] hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-300" />
							)}
						</div>

						{/* Legal Links Section */}
						<div className="lg:col-span-6">
							<div className="flex flex-col space-y-4">
								{companyInfo.legalLinks && companyInfo.legalLinks.length > 0 && (
									<div className="flex flex-wrap justify-center gap-4">
										{companyInfo.legalLinks.map((link, index) => (
											<Link
												key={index}
												href={link.href}
												className="text-[#231645]/50 hover:underline dark:text-[#606060] hover:text-gray-900 dark:hover:text-zinc-200 text-sm transition-colors"
											>
												{link.text}
											</Link>
										))}
									</div>
								)}
								<div className="text-center">
									<span className="text-sm text-[#231645]/50 dark:text-[#606060]">
										{companyInfo.copyright}
									</span>
								</div>
								{companyInfo.disclaimer && (
									<p className="text-xs text-[#231645]/50 dark:text-[#606060] text-center">
										{companyInfo.disclaimer}
									</p>
								)}
							</div>
						</div>

						{/* Theme and System Status Section */}
						<div className="lg:col-span-3 flex flex-col items-center lg:items-end gap-4">
							<ThemeToggle />
							{systemStatus && <SystemStatus {...systemStatus} />}
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default FooterBlock;
