{"name": "@cloc/builder", "version": "0.1.0", "license": "AGPL-3.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@builder.io/dev-tools": "^1.0.17", "@builder.io/react": "^3.2.12", "@builder.io/sdk": "^2.2.9", "@cloc/atoms": "*", "@cloc/ui": "*", "@craftjs/core": "^0.2.7", "@craftjs/layers": "^0.2.5", "@paperbits/common": "^0.1.613", "@paperbits/core": "^0.1.613", "@paperbits/react": "^1.0.8", "@paperbits/styles": "^0.1.613", "@plasmicapp/loader": "^0.0.59", "@plasmicapp/loader-nextjs": "^1.0.404", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/styled-components": "^5.1.34", "axios": "^1.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "debounce": "^2.1.1", "embla-carousel-react": "^8.2.0", "framer-motion": "^11.3.28", "grapejs": "^2.1.2", "grapesjs": "^0.21.12", "grapesjs-blocks-basic": "^1.0.2", "grapesjs-mjml": "^1.0.6", "grapesjs-plugin-ckeditor": "^1.0.1", "grapesjs-plugin-forms": "^2.0.6", "grapesjs-preset-webpage": "^1.0.3", "grapesjs-tailwind": "^1.0.11", "grapesjs-tailwind-typeahead": "^1.5.5", "jotai": "^2.9.3", "lucide-react": "^0.412.0", "lzutf8": "^0.6.3", "next": "15.2.4", "next-themes": "^0.4.4", "prism-react-renderer": "^2.4.1", "prop-types": "latest", "re-resizable": "^6.10.0", "react": "^19", "react-color": "^2.19.3", "react-contenteditable": "^3.3.7", "react-dom": "^19", "recoil": "^0.7.7", "reka": "^0.0.0", "styled-components": "^6.1.13", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-color": "^3.0.12", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "clsx": "^2.1.1", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}