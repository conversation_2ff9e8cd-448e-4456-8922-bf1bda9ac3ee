# Visual Builders

Here is the list of npm libraries that provide visual drag and drop builder for creating custom components.

-   **BuilderIO/builder**: A powerful visual builder for creating dynamic content.
-   **DND-KIT**: A lightweight, performant, accessible and extensible drag & drop toolkit for React.
-   **Craft.js**: A React framework for building powerful drag-and-drop page editors.
-   **Reka.js**: A state management system for building no-code editors with an AST-based data structure.

### Setup Instructions

Install the required packages:

```sh
npm install
# or
yarn
# or
pnpm install
```

Run the development server:

```sh
npm run dev
# or
yarn run dev
# or
pnpm run dev
```

Open the following URLs to see examples:

-   Craft.js http://localhost:3000/craft
-   Builder.io http://localhost:3000/builder/builder-demo
-   dnd-kit http://localhost:3000/dnd
-   Reka.js http://localhost:3000/reka
