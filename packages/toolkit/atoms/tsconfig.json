{"extends": "@cloc/typescript-config/react-library.json", "exclude": ["dist", "build", "node_modules", "tailwind.config.ts"], "include": ["src/**/*.ts", "src/**/*.tsx"], "compilerOptions": {"strictNullChecks": true, "strict": true, "baseUrl": "./", "paths": {"@components/*": ["src/lib/components/*"], "@hooks/*": ["src/lib/hooks/*"], "@libs/*": ["src/lib/libs/*"], "@lib/*": ["src/lib/*"], "@utils/*": ["src/lib/utils/*"], "@cloc/atoms/link-adapter/next": ["src/lib/components/universal/link-adapter.next"], "@cloc/atoms/link-adapter/remix": ["src/lib/components/universal/link-adapter.remix"], "@cloc/atoms/link-adapter/native": ["src/lib/components/universal/link-adapter.native"]}, "outDir": "dist", "rootDir": "./src", "importHelpers": false}}