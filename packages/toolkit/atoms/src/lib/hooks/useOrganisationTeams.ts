import { useEffect } from 'react';
import { PaginationResponse, IOrganizationTeamList, IUser, IHookResponse } from '@cloc/types';
import { getOrganisationTeams } from '@cloc/api';
import { useAtom } from 'jotai';
import { organizationTeamsAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { toast } from '@cloc/ui';

export const useOrganizationTeams = (
	user: IUser | null,
	token: string,
	projectId: string | null,
	organizationId: string
): IHookResponse<PaginationResponse<IOrganizationTeamList>> => {
	const [teams, setTeams] = useAtom(organizationTeamsAtom);

	useEffect(() => {
		if (user && organizationId) {
			(async () => {
				setTeams((prev) => ({ ...prev, loading: true }));

				const userTeams = await getOrganisationTeams(user, token, organizationId);

				if ('message' in userTeams || 'error' in userTeams) {
					const errorMessage =
						'message' in userTeams
							? Array.isArray(userTeams.message)
								? userTeams.message.join(', ')
								: userTeams.message
							: String(userTeams.error);

					setTeams((prev) => ({ ...prev, loading: false }));

					toast({
						description: errorMessage,
						variant: 'destructive'
					});
					return;
				}
				setTeams({ data: userTeams, loading: false });
			})();
		}
	}, [user, projectId, organizationId]);

	return teams;
};
