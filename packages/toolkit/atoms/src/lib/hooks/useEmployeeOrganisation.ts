import { useEffect } from 'react';
import { IUser, IHookResponse, IOrganizationContact } from '@cloc/types';
import { getOrganizationClients } from '@cloc/api';
import { useAtom } from 'jotai';
import { organizationClientsAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { toast } from '@cloc/ui';

export const useEmployeeOrganization = (
	user: IUser | null,
	token: string,
	organizationId: string
): IHookResponse<IOrganizationContact[]> => {
	const [clients, setClients] = useAtom(organizationClientsAtom);
	useEffect(() => {
		if (user && organizationId) {
			(async () => {
				setClients((prev) => ({ ...prev, loading: true }));
				const userClients = await getOrganizationClients(user, token, organizationId);

				if ('message' in userClients || 'error' in userClients) {
					const errorMessage =
						'message' in userClients
							? Array.isArray(userClients.message)
								? userClients.message.join(', ')
								: userClients.message
							: String(userClients.error);

					toast({
						variant: 'destructive',
						description: errorMessage
					});
					setClients((prev) => ({ ...prev, loading: false }));

					return;
				}

				setClients({ data: userClients, loading: false });
			})();
		} else {
			setClients({ data: [], loading: false });
		}
	}, [user, token, organizationId]);

	return clients;
};
