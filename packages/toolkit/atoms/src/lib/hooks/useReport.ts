import { useEffect } from 'react';

import { ApiCall } from '@cloc/api';
import { IHookResponse, IUser, IWeeklyReports } from '@cloc/types';
import qs from 'qs';
import { getErrorMessage, reportError, toast } from '@cloc/ui';
import { DateRange } from 'react-day-picker';
import { useAtom } from 'jotai';
import { reportAtom } from '../cloc-jotai/atoms/cloc-atoms';

const useReport = (
	user: IUser | null,
	token: string,
	selectedEmployee: string,
	selectedTeam: string,
	organizationId: string,
	date?: DateRange
): IHookResponse<IWeeklyReports> => {
	const [report, setReport] = useAtom(reportAtom);

	const getReport = async () => {
		setReport((report) => ({ ...report, loading: true }));
		try {
			if (!date) {
				reportError('Invalid dates');
				setReport((elt) => {
					return { ...elt, loading: false };
				});
				return;
			}

			if (!user) throw new Error('User is not authenticated');

			const employeeId = user.employee
				? user.employee.id
				: selectedEmployee !== 'all'
					? selectedEmployee
					: undefined;
			const { tenantId } = user;
			const teamId = selectedTeam !== 'all' ? selectedTeam : undefined;

			const queryWeeklyReport = qs.stringify(
				{
					tenantId,
					organizationId,
					'activityLevel[start]': 0,
					'activityLevel[end]': 100,
					startDate: date?.from?.toISOString(),
					endDate: date?.to?.toISOString(),
					timeZone: user?.timeZone?.split(' ')[0] || Intl.DateTimeFormat().resolvedOptions().timeZone,
					...(employeeId ? { employeeIds: [employeeId] } : {}),
					...(teamId ? { teamIds: [teamId] } : {})
				},
				{ skipNulls: true }
			);

			const report = await ApiCall<IWeeklyReports>({
				path: `/timesheet/time-log/report/weekly?${queryWeeklyReport}`,
				method: 'GET',
				bearer_token: token
			});

			if ('data' in report) setReport((prev) => ({ ...prev, data: report.data }));
			else {
				toast({
					title: 'Ever Cloc Error',
					description: report.message || report.error,
					variant: 'destructive'
				});
			}
		} catch (error) {
			toast({
				title: 'Ever Cloc Error',
				description: 'Report error : ' + getErrorMessage(error),
				variant: 'destructive'
			});
		} finally {
			setReport((report) => ({ ...report, loading: false }));
		}
	};

	useEffect(() => {
		if (user && organizationId) getReport();
	}, [user, date, selectedEmployee, selectedTeam, organizationId]);

	return report;
};

export { useReport };
