import { useEffect } from 'react';

import { getTimerStatus } from '@cloc/api';
import { IHookResponse, ITimerStatus, IUser } from '@cloc/types';
import { useAtom } from 'jotai';
import { timerStatusAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { toast } from '@cloc/ui';

const useTimerStatus = (user: IUser | null, token: string, organizationId?: string): IHookResponse<ITimerStatus> => {
	const [timerStatus, setTimerStatus] = useAtom(timerStatusAtom);

	const fetchTimerStatus = async (): Promise<void> => {
		try {
			setTimerStatus((prev) => ({ ...prev, loading: true }));
			const userTimerStatus = await getTimerStatus(user, token, organizationId);

			if ('message' in userTimerStatus || 'error' in userTimerStatus) {
				const errorMessage =
					'message' in userTimerStatus
						? Array.isArray(userTimerStatus.message)
							? userTimerStatus.message.join(', ')
							: userTimerStatus.message
						: String(userTimerStatus.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});

				return;
			}

			setTimerStatus((prev) => ({ ...prev, data: userTimerStatus, loading: false }));
		} catch (error) {
			toast({
				variant: 'destructive',
				description: (error as Error).message
			});
		} finally {
			setTimerStatus((prev) => ({ ...prev, loading: false }));
		}
	};
	useEffect(() => {
		if (user && user.employee && token && organizationId) {
			fetchTimerStatus();
		}
	}, [user, organizationId]);

	return timerStatus;
};

export { useTimerStatus };
