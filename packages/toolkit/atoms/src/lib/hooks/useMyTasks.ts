import { getMyTasks } from '@cloc/api';
import { useEffect } from 'react';
import { IHookResponse, ITeamTask, IUser } from '@cloc/types';
import { useAtom } from 'jotai';
import { tasksAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { toast } from '@cloc/ui';

const useMyTasks = (
	user: IUser | null,
	token: string,
	projectId: string | null,
	organizationId: string
): IHookResponse<ITeamTask[] | null> => {
	const [tasks, setTasks] = useAtom(tasksAtom);

	useEffect(() => {
		if (user && organizationId) {
			(async () => {
				setTasks((prev) => ({ ...prev, loading: true }));
				const userTasks = await getMyTasks(user, token, projectId, organizationId);

				if ('message' in userTasks || 'error' in userTasks) {
					const errorMessage =
						'message' in userTasks
							? Array.isArray(userTasks.message)
								? userTasks.message.join(', ')
								: userTasks.message
							: String(userTasks.error);

					toast({
						variant: 'destructive',
						description: errorMessage
					});
					setTasks((prev) => ({ ...prev, loading: false }));

					return;
				}
				setTasks({ data: userTasks.items, loading: false });
			})();
		}
	}, [user, projectId, organizationId]);

	return tasks;
};

export { useMyTasks };
