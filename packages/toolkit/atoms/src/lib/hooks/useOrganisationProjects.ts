import { useEffect } from 'react';
import { IUser, IProject, IHookResponse } from '@cloc/types';
import { getOrganisationProjects } from '@cloc/api';
import { useAtom } from 'jotai';
import { organizationProjectsAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { toast } from '@cloc/ui';

export const useOrganizationProjects = (
	user: IUser | null,
	token: string,
	selectedClient: string | null,
	organizationId: string
): IHookResponse<IProject[]> => {
	const [projects, setProjects] = useAtom(organizationProjectsAtom);

	useEffect(() => {
		if (user && organizationId) {
			(async () => {
				setProjects((prev) => ({ ...prev, loading: true }));
				const userProjects = await getOrganisationProjects(user, token, selectedClient, organizationId);

				if ('message' in userProjects || 'error' in userProjects) {
					const errorMessage =
						'message' in userProjects
							? Array.isArray(userProjects.message)
								? userProjects.message.join(', ')
								: userProjects.message
							: String(userProjects.error);

					toast({
						variant: 'destructive',
						description: errorMessage
					});
					setProjects((prev) => ({ ...prev, loading: false }));

					return;
				}

				setProjects({ data: userProjects.items, loading: false });
			})();
		}
	}, [user, selectedClient, organizationId]);

	return projects;
};
