import { IHookResponse, IMember, IPermission, IUser, PaginationResponse } from '@cloc/types';
import { getWeekStartAndEnd, toast } from '@cloc/ui';
import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { membersAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { getMembers } from '@cloc/api';

const useMember = (
	user: IUser | null,
	token: string,
	userPermissions: IPermission[] | null,
	organizationId: string
): IHookResponse<PaginationResponse<IMember> | null> => {
	const [members, setMembers] = useAtom(membersAtom);

	const fetchMembers = async () => {
		if (user && organizationId && token) {
			setMembers((prev) => ({ ...prev, loading: true }));

			const userMembers = await getMembers(user, token, organizationId, getWeekStartAndEnd());

			if ('message' in userMembers || 'error' in userMembers) {
				const errorMessage =
					'message' in userMembers
						? Array.isArray(userMembers.message)
							? userMembers.message.join(', ')
							: userMembers.message
						: String(userMembers.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});
				setMembers((prev) => ({ ...prev, loading: false }));

				return;
			}

			setMembers((prev) => ({ ...prev, data: userMembers, loading: false }));
		} else {
			setMembers({ data: { items: [], total: 0 }, loading: false });
		}
	};

	useEffect(() => {
		fetchMembers();
	}, [user, userPermissions, organizationId, token]);

	return members;
};

export { useMember };
