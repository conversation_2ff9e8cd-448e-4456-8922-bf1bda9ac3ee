import { IHookResponse, IUser } from '@cloc/types';
import { getActivitiesStats } from '@cloc/api';
import { activitiesStatsAtom } from '../cloc-jotai/atoms/cloc-atoms';
import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { getStartAndEndOfDay, getWeekStartAndEnd, toast } from '@cloc/ui';
import { IActivitiesStats } from '@cloc/types';
import { DateRange } from 'react-day-picker';

const useActivitiesStats = (
	user: IUser | null,
	token: string,
	selectedEmployee: string,
	selectedTeam: string,
	organizationId: string,
	date?: DateRange
): IHookResponse<IActivitiesStats> => {
	const [activitiesStats, setActivitiesStats] = useAtom(activitiesStatsAtom);

	const fetchActivitiesStats = async () => {
		if (user && organizationId) {
			setActivitiesStats((prev) => ({ ...prev, loading: true }));

			const dayDates = getStartAndEndOfDay();
			const { end, start } = getWeekStartAndEnd();

			const userStats = await getActivitiesStats({
				user,
				token,
				selectedEmployee,
				selectedTeam,
				organizationId,
				dayDates,
				dates: {
					from: date?.from || start,
					to: date?.to || end
				}
			});

			if ('message' in userStats || 'error' in userStats) {
				const errorMessage =
					'message' in userStats
						? Array.isArray(userStats.message)
							? userStats.message.join(', ')
							: userStats.message
						: String(userStats.error);

				toast({
					variant: 'destructive',
					description: errorMessage
				});
				setActivitiesStats((prev) => ({ ...prev, loading: false }));
				return;
			}

			setActivitiesStats({ data: userStats, loading: false });
		}
	};

	useEffect(() => {
		fetchActivitiesStats();
	}, [user, selectedEmployee, selectedTeam, date, organizationId]);

	return activitiesStats;
};

export { useActivitiesStats };
