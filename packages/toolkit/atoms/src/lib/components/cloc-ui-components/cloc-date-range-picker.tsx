/** @jsxImportSource theme-ui */
'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn, Button, Calendar, Popover, PopoverContent, PopoverTrigger, PopoverClose } from '@cloc/ui';
import { IDateRangePickerProps } from '@cloc/types';
import { DateRange } from 'react-day-picker';
import { useClocContext } from '@lib/context/cloc-context';

export function ClocDateRangePicker({ className, date, setDate, size }: IDateRangePickerProps) {
	const [pickedDate, setPickedDate] = React.useState<DateRange | undefined>(date);
	const { appliedTheme } = useClocContext();
	React.useEffect(() => {
		setPickedDate(date);
	}, [date]);

	return (
		<div className={cn('grid gap-2', className)}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						id="date"
						variant={'outline'}
						size={size}
						className={cn(' justify-start text-left text-xs font-normal', !date && 'text-muted-foreground')}
					>
						<CalendarIcon className="mr-2 h-4 w-4" />
						{date?.from ? (
							date.to ? (
								<>
									{format(date.from, 'LLL dd, y')} - {format(date.to, 'LLL dd, y')}
								</>
							) : (
								format(date.from, 'LLL dd, y')
							)
						) : (
							<>
								<span>Pick dates (Start/End)</span>
							</>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="flex flex-col items-end gap-2 w-auto " align="start">
					<Calendar
						initialFocus
						mode="range"
						defaultMonth={pickedDate?.from}
						selected={pickedDate}
						onSelect={setPickedDate}
						numberOfMonths={2}
						modifiersClassNames={{
							start: '!after:hidden text-white !rounded-full',
							end: '!after:hidden text-white !rounded-full'
						}}
						modifiersStyles={{
							start: {
								backgroundColor: appliedTheme.colors?.borderColor as string
							},
							end: {
								backgroundColor: appliedTheme.colors?.borderColor as string
							},
							selected: {
								backgroundColor: appliedTheme.colors?.borderColor as string
							}
						}}
					/>

					<div className="flex gap-3">
						<PopoverClose asChild>
							<Button className="w-20" variant={'secondary'} size={'sm'}>
								Close
							</Button>
						</PopoverClose>
						<PopoverClose asChild className="flex gap-3">
							<Button
								sx={{ background: 'mainColor' }}
								className={`w-20 `}
								onClick={() => setDate?.(pickedDate)}
								size={'sm'}
								style={{ backgroundColor: appliedTheme.colors?.borderColor as string }}
							>
								Ok
							</Button>
						</PopoverClose>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
}
