import { IClocProgressProps } from '@cloc/types';
import { cn } from '@cloc/ui';
import { Progress } from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';

const ClocProgress = ({ className }: IClocProgressProps) => {
	const {
		todayTrackedTime: { totalSeconds }
	} = useClocContext();

	const dailyPickHours = 8 * 60 * 60; // 8 hours in seconds

	return <Progress className={cn(className)} value={(totalSeconds * 100) / dailyPickHours} />;
};

ClocProgress.displaName = 'clocProgress';

export { ClocProgress };
