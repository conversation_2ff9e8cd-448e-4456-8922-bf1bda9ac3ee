import { Card, cn, Progress } from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';
import { SpinOverlayLoader } from '../loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';

export interface IProjectDisplayer {
	icon?: React.ReactNode;
	workedprojects?: number;
	totalProjects?: number;
	label: string;
	showProgress?: boolean;
	className?: string;
}

const ClocProjectDisplayer: React.FC<IProjectDisplayer> = ({
	icon,
	workedprojects = 0,
	totalProjects = 0,
	label,
	showProgress = true,
	className
}) => {
	const {
		loadings: { statisticsCountsLoading }
	} = useClocContext();
	return (
		<Card
			className={cn(
				'dark:text-white border relative dark:border-gray-600 text-sm rounded-xl p-3 min-w-[150px]  gap-1 inline-flex  flex-col',
				className
			)}
		>
			{statisticsCountsLoading && <SpinOverlayLoader />}
			<div className="flex justify-between items-center dark:text-white/50 text-gray-400">
				<span className="text-xs">{label}</span>
				<span>{icon}</span>
			</div>
			<div className="text-xl font-medium ">{workedprojects}</div>
			{showProgress && <Progress className="w-full h-2" value={(workedprojects / totalProjects) * 100} />}
		</Card>
	);
};

const WorkedProjectDisplayer = ({ showProgress, className }: { showProgress?: boolean; className?: string }) => {
	const { statisticsCounts, organizationProjects } = useClocContext();
	const { t } = useTranslation();
	return (
		<ClocProjectDisplayer
			workedprojects={statisticsCounts?.projectsCount}
			totalProjects={organizationProjects?.length}
			showProgress={showProgress}
			label={t('COMMON.project')}
			className={className}
		/>
	);
};

export { WorkedProjectDisplayer, ClocProjectDisplayer };
