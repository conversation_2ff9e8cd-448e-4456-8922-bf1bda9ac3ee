'use client';

import React from 'react';
import Container from '../common/container';
import type { BasicTimerProps } from '../basic-timer/timer';
import TimerIcon from '../basic-timer/timer-icon';
import Clock from '../common/clock';
import TimerButton from '../basic-timer/timer-button';
import TimerTime from './timer-time';

interface clocCustomTimer extends BasicTimerProps {
	labeled?: boolean;
}

const clocCustomTimer = ({
	ref,
	time,
	color,
	icon,
	iconClassName,
	buttonClassName,
	customIcon,
	customButtonIcon,
	isRunning,
	readonly,
	labeled,
	...props
}: clocCustomTimer & {
	ref?: React.RefObject<HTMLButtonElement>;
}) => {
	const { background, rounded, ...rest } = props;
	return (
		<Container ref={ref} {...rest}>
			{icon ? (
				<TimerIcon
					icon={<Clock />}
					{...(customIcon && { icon: customIcon })}
					className={iconClassName}
					size={labeled ? 'extra-large' : 'large'}
					variant={color === 'primary' ? 'primary' : 'secondary'}
				/>
			) : null}
			<TimerTime background={background} color={color} labeled={labeled} rounded={rounded} time={time} />
			{!readonly && (
				<TimerButton
					{...(customButtonIcon && { icon: customButtonIcon })}
					className={buttonClassName}
					isRunning={isRunning}
					size={labeled ? 'extra-large' : 'large'}
					variant="primary"
				/>
			)}
		</Container>
	);
};

clocCustomTimer.displayName = 'clocBasicTimer';
export default clocCustomTimer;
