import React from 'react';
import { Minus } from 'lucide-react';
import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import { cn, getTimeDigits } from '@cloc/ui';
import type { Time, TimeVariances } from '../basic-timer/timer-time';
import { timeVariants } from '../basic-timer/timer-time';

export const digitsVariants = cva('w-8 flex flex-col justify-center items-center p-1 ', {
	variants: {
		background: {
			none: 'transparent',
			destructive: 'bg-white dark:bg-black',
			secondary: 'bg-gray-300/70 dark:bg-gray-700',
			primary: 'bg-black dark:bg-white'
		},
		rounded: {
			none: 'rounded-none',
			small: 'rounded-xl',
			medium: 'rounded-2xl',
			large: 'rounded-3xl'
		}
	},
	defaultVariants: {
		background: 'primary',
		rounded: 'none'
	}
});

export type DigitsVariances = VariantProps<typeof digitsVariants>;

export interface TimerTimeProps extends TimeVariances, DigitsVariances {
	time: Time;
	sx?: React.CSSProperties;
	labeled?: boolean;
}

// TODO: Must move this to be used dynamically
const Separator: React.FC = () => (
	<span className="inline-flex items-center justify-center">
		<Minus className="stroke-gray-400" size={10} />
	</span>
);

const TimerTime: React.FC<TimerTimeProps> = ({ time, sx, labeled, color, background, rounded }) => {
	const { hours, minutes, seconds } = time;

	return (
		<div className={cn('inline-flex items-center justify-center text-4xl font-bold', timeVariants({ color }), sx)}>
			<div className="flex flex-col justify-center">
				<div className="flex gap-1">
					{([hours, minutes, seconds] as number[]).map((unit, index) => (
						<React.Fragment key={unit}>
							<div className={cn(digitsVariants({ background, rounded }))}>
								<span>{getTimeDigits(unit).split('')[0]}</span>
							</div>
							<div className={cn(digitsVariants({ background, rounded }))}>
								<span>{getTimeDigits(unit).split('')[1]}</span>
							</div>
							{index < 2 && <Separator />}
						</React.Fragment>
					))}
				</div>
				{labeled ? (
					<div className="flex justify-around">
						<span className="text-xs">Hours</span>
						<span className="text-xs">Minutes</span>
						<span className="text-xs">Seconds</span>
					</div>
				) : null}
			</div>
		</div>
	);
};

export default TimerTime;
