'use client';

import Pause from '../common/pause';
import Play from '../common/play';
import type ClocBasicTimer from './timer';
import BaseAtom from './timer';
import { useClocContext } from '@lib/context/cloc-context';
interface ClocBasicProps extends Omit<ClocBasicTimer, 'time' | 'progress'> {
	progress?: boolean;
}

// TODO: Must move this to Atoms and integrate with gauzy api
export function ClocExtra({ progress, ...props }: Readonly<ClocBasicProps>) {
	const {
		todayTrackedTime: { hours, minutes, seconds, totalSeconds },
		startTimer,
		stopTimer,
		isRunning,
		timerLoading,
		authenticatedUser
	} = useClocContext();

	const ActionButton = isRunning ? Pause : Play;

	return (
		<BaseAtom
			onClick={isRunning ? stopTimer : startTimer}
			{...props}
			isRunning={isRunning}
			{...(progress && { progress: (seconds / 100) * 60 })}
			customButtonIcon={<ActionButton />}
			time={{
				hours,
				minutes,
				seconds,
				totalSeconds
			}}
			disabled={timerLoading || !authenticatedUser}
		/>
	);
}

export function DefaultClocExtra() {
	return <ClocExtra readonly />;
}

export function ClocExtraGray() {
	return <ClocExtra background="secondary" readonly />;
}

export function ClocExtraGrayRounded() {
	return <ClocExtra background="secondary" readonly rounded="small" />;
}

export function ClocExtraContained() {
	return <ClocExtra background="secondary" color="primary" readonly />;
}

export function ClocExtraContainedRounded() {
	return <ClocExtra background="secondary" color="primary" readonly rounded="small" />;
}

export function ClocExtraIcon() {
	return <ClocExtra icon readonly />;
}
export function ClocExtraIconGray() {
	return <ClocExtra background="secondary" icon readonly />;
}

export function ClocExtraIconGrayRounded() {
	return <ClocExtra background="secondary" icon readonly rounded="small" />;
}

export function ClocExtraIconContained() {
	return <ClocExtra background="secondary" color="primary" icon readonly />;
}

export function ClocExtraIconContainedRounded() {
	return <ClocExtra background="secondary" color="primary" icon readonly rounded="small" />;
}

export function ClocExtraIconProgressButton() {
	return <ClocExtra icon labeled />;
}

export function ClocExtraIconGrayButton() {
	return <ClocExtra icon labeled />;
}

export function ClocExtraIconGrayRoundedButton() {
	return <ClocExtra icon labeled rounded="small" />;
}

export function ClocExtraIconContainedButton() {
	return <ClocExtra background="secondary" color="primary" icon labeled />;
}

export function ClocExtraIconContainedRoundedButton() {
	return <ClocExtra background="secondary" color="primary" icon labeled rounded="small" />;
}
