/** @jsxImportSource theme-ui */

'use client';

import Pause from '../common/pause';
import Play from '../common/play';
import type ClocBasicTimer from './timer';
import BaseAtom from './timer';
import { useClocContext } from '@lib/context/cloc-context';
import { TimeFormat } from '@lib/constants';

export interface ClocBasicProps extends Omit<ClocBasicTimer, 'time' | 'progress'> {
	progress?: boolean;
	format?: TimeFormat;
	separator?: string;
}

// TODO: Must move this to Atoms and integrate with gauzy api
export function ClocBasic({ progress, ...props }: Readonly<ClocBasicProps>) {
	const {
		todayTrackedTime: { hours, minutes, seconds, totalSeconds },
		startTimer,
		stopTimer,
		isRunning,
		timerLoading,
		authenticatedUser
	} = useClocContext();

	const ActionButton = isRunning ? Pause : Play;

	return (
		<BaseAtom
			onClick={isRunning ? stopTimer : startTimer}
			{...props}
			isRunning={isRunning}
			{...(progress && { progress: (seconds / 100) * 60 })}
			customButtonIcon={<ActionButton />}
			time={{
				hours,
				minutes,
				seconds,
				totalSeconds
			}}
			disabled={timerLoading || !authenticatedUser}
		/>
	);
}

export function DefaultClocBasic() {
	return <ClocBasic readonly />;
}

export function ClocBasicBorder() {
	return <ClocBasic border="thick" readonly />;
}

export function ClocBasicBorderRounded() {
	return <ClocBasic border="thick" readonly rounded="small" />;
}

export function ClocBasicBorderFullRounded() {
	return <ClocBasic border="thick" readonly rounded="large" />;
}

export function ClocBasicGray() {
	return <ClocBasic background="secondary" readonly />;
}

export function ClocBasicGrayRounded() {
	return <ClocBasic background="secondary" readonly rounded="small" />;
}

export function ClocBasicGrayFullRounded() {
	return <ClocBasic background="secondary" sx={{ background: 'mainColor' }} readonly rounded="large" />;
}

export function ClocBasicContained() {
	return <ClocBasic background="primary" sx={{ background: 'mainColor' }} color="destructive" readonly />;
}

export function ClocBasicContainedRounded() {
	return (
		<ClocBasic sx={{ background: 'mainColor' }} background="primary" color="destructive" readonly rounded="small" />
	);
}

export function ClocBasicContainedFullRounded() {
	return <ClocBasic background="primary" color="destructive" readonly rounded="large" />;
}

export function ClocBasicIcon() {
	return <ClocBasic icon readonly />;
}
export function ClocBasicIconBorder() {
	return <ClocBasic border="thick" icon readonly />;
}

export function ClocBasicIconBorderRounded() {
	return <ClocBasic border="thick" icon readonly rounded="small" />;
}

export function ClocBasicIconBorderFullRounded() {
	return <ClocBasic border="thick" icon readonly rounded="large" />;
}

export function ClocBasicIconGray() {
	return <ClocBasic icon background="secondary" readonly />;
}

export function ClocBasicIconGrayRounded() {
	return <ClocBasic icon readonly background="secondary" rounded="small" />;
}

export function ClocBasicIconGrayFullRounded() {
	return <ClocBasic icon readonly background="secondary" rounded="large" />;
}

export function ClocBasicIconContained() {
	return <ClocBasic icon background="primary" color="destructive" readonly />;
}

export function ClocBasicIconContainedRounded() {
	return <ClocBasic icon background="primary" color="destructive" readonly rounded="small" />;
}

export function ClocBasicIconContainedFullRounded() {
	return <ClocBasic icon background="primary" color="destructive" readonly rounded="large" />;
}

export function ClocBasicIconProgress() {
	return <ClocBasic icon progress readonly />;
}
export function ClocBasicIconBorderProgress() {
	return <ClocBasic border="thick" icon progress readonly />;
}

export function ClocBasicIconBorderRoundedProgress() {
	return <ClocBasic border="thick" icon progress readonly rounded="small" />;
}

export function ClocBasicIconBorderFullRoundedProgress() {
	return <ClocBasic border="thick" icon progress readonly rounded="large" />;
}

export function ClocBasicIconGrayProgress() {
	return <ClocBasic background="secondary" icon progress readonly />;
}

export function ClocBasicIconGrayRoundedProgress() {
	return <ClocBasic background="secondary" icon progress readonly rounded="small" />;
}

export function ClocBasicIconGrayFullRoundedProgress() {
	return <ClocBasic icon progress background="secondary" readonly rounded="large" />;
}

export function ClocBasicIconContainedProgress() {
	return <ClocBasic icon progress background="primary" color="destructive" readonly />;
}

export function ClocBasicIconContainedRoundedProgress() {
	return <ClocBasic icon progress background="primary" color="destructive" readonly rounded="small" />;
}

export function ClocBasicIconContainedFullRoundedProgress() {
	return <ClocBasic icon progress background="primary" color="destructive" readonly rounded="large" />;
}

export function ClocBasicIconProgressButton() {
	return <ClocBasic icon labeled progress />;
}
export function ClocBasicIconBorderProgressButton() {
	return <ClocBasic border="thick" icon labeled progress />;
}

export function ClocBasicIconBorderRoundedProgressButton() {
	return <ClocBasic border="thick" icon labeled progress rounded="small" />;
}

export function ClocBasicIconBorderFullRoundedProgressButton() {
	return <ClocBasic border="thick" icon labeled progress rounded="large" />;
}

export function ClocBasicIconGrayProgressButton() {
	return <ClocBasic background="secondary" icon labeled progress />;
}

export function ClocBasicIconGrayRoundedProgressButton() {
	return <ClocBasic background="secondary" icon labeled progress rounded="small" />;
}

export function ClocBasicIconGrayFullRoundedProgressButton() {
	return <ClocBasic icon labeled progress background="secondary" rounded="large" />;
}

export function ClocBasicIconContainedProgressButton() {
	return <ClocBasic background="primary" color="destructive" icon labeled progress />;
}

export function ClocBasicIconContainedRoundedProgressButton() {
	return <ClocBasic background="primary" color="destructive" icon labeled progress rounded="small" />;
}

export function ClocBasicIconContainedFullRoundedProgressButton() {
	return <ClocBasic background="primary" color="destructive" icon labeled progress rounded="large" />;
}

export function ClocBasicCompact() {
	return <ClocBasic readonly format={TimeFormat.COMPACT} />;
}

export function ClocBasicHoursMinutes() {
	return <ClocBasic readonly format={TimeFormat.HOURS_MINUTES} />;
}

export function ClocBasicWords() {
	return <ClocBasic readonly format={TimeFormat.WORDS} />;
}

export function ClocBasicMinimal() {
	return <ClocBasic readonly format={TimeFormat.MINIMAL} />;
}
