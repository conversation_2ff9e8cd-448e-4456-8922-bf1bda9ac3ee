'use client';

import React from 'react';
import Container from '../common/container';
import type { BasicTimerProps } from '../basic-timer/timer';
import TimerIcon from '../basic-timer/timer-icon';
import Clock from '../common/clock';
import TimerButton from '../basic-timer/timer-button';
import TimerTime from './timer-time';
import { TimeFormat } from '@lib/constants';

interface clocBasicTimer extends BasicTimerProps {
	labeled?: boolean;
	format?: TimeFormat;
	separator?: string;
}

const clocBasicTimer = ({
	ref,
	time,
	color,
	icon,
	iconClassName,
	buttonClassName,
	customIcon,
	customButtonIcon,
	isRunning,
	readonly,
	labeled,
	format = TimeFormat.DEFAULT,
	separator = 'default',
	...props
}: clocBasicTimer & {
	ref?: React.RefObject<HTMLButtonElement>;
}) => {
	return (
		<Container className="gap-3" ref={ref} {...props}>
			{icon ? (
				<TimerIcon
					icon={<Clock />}
					{...(customIcon && { icon: customIcon })}
					className={iconClassName}
					size={labeled ? 'extra-large' : 'large'}
					variant={color}
				/>
			) : null}
			<TimerTime color={color} labeled={labeled} time={time} format={format} separator={separator} />
			{!readonly && (
				<TimerButton
					{...(customButtonIcon && { icon: customButtonIcon })}
					className={buttonClassName}
					isRunning={isRunning}
					size={labeled ? 'extra-large' : 'large'}
					variant={color}
				/>
			)}
		</Container>
	);
};

clocBasicTimer.displayName = 'clocBasicTimer';
export default clocBasicTimer;
