'use client';

import Pause from '../common/pause';
import Play from '../common/play';
import type ClocBasicTimer from './timer';
import BaseAtom from './timer';

import { useClocContext } from '@lib/context/cloc-context';

export interface ClocCustomProps extends Omit<ClocBasicTimer, 'time' | 'progress'> {
	progress?: boolean;
}

// TODO: Must move this to Atoms and integrate with gauzy api
export function ClocCustom({ progress, ...props }: Readonly<ClocCustomProps>) {
	const {
		todayTrackedTime: { hours, minutes, seconds, totalSeconds },
		startTimer,
		stopTimer,
		isRunning,
		timerLoading,
		authenticatedUser
	} = useClocContext();

	const ActionButton = isRunning ? Pause : Play;

	return (
		<BaseAtom
			onClick={isRunning ? stopTimer : startTimer}
			{...props}
			isRunning={isRunning}
			{...(progress && { progress: (seconds / 100) * 60 })}
			customButtonIcon={<ActionButton />}
			time={{
				hours,
				minutes,
				seconds,
				totalSeconds
			}}
			disabled={timerLoading || !authenticatedUser}
		/>
	);
}

export function DefaultClocCustom() {
	return <ClocCustom readonly />;
}

export function ClocCustomGray() {
	return <ClocCustom background="secondary" readonly />;
}

export function ClocCustomGrayRounded() {
	return <ClocCustom background="secondary" readonly rounded="small" />;
}

export function ClocCustomContained() {
	return <ClocCustom background="secondary" color="primary" readonly />;
}

export function ClocCustomContainedRounded() {
	return <ClocCustom background="secondary" color="primary" readonly rounded="small" />;
}

export function ClocCustomIcon() {
	return <ClocCustom icon readonly />;
}
export function ClocCustomIconGray() {
	return <ClocCustom background="secondary" icon readonly />;
}

export function ClocCustomIconGrayRounded() {
	return <ClocCustom background="secondary" icon readonly rounded="small" />;
}

export function ClocCustomIconContained() {
	return <ClocCustom background="secondary" color="primary" icon readonly />;
}

export function ClocCustomIconContainedRounded() {
	return <ClocCustom background="secondary" color="primary" icon readonly rounded="small" />;
}

export function ClocCustomIconProgressButton() {
	return <ClocCustom icon labeled />;
}

export function ClocCustomIconGrayButton() {
	return <ClocCustom background="secondary" icon labeled />;
}

export function ClocCustomIconGrayRoundedButton() {
	return <ClocCustom background="secondary" icon labeled rounded="small" />;
}

export function ClocCustomIconContainedButton() {
	return <ClocCustom background="secondary" color="primary" icon labeled />;
}

export function ClocCustomIconContainedRoundedButton() {
	return <ClocCustom background="secondary" color="primary" icon labeled rounded="small" />;
}
