import React from 'react';

const Clock = function Clock({
	ref,
	...props
}: React.SVGProps<SVGSVGElement> & {
	ref?: React.RefObject<SVGSVGElement>;
}) {
	return (
		<svg
			fill="none"
			height="56"
			ref={ref}
			stroke="currentColor"
			viewBox="0 0 56 56"
			width="56"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<path
				clipRule="evenodd"
				d="M24.604 1.49002C24.604 0.667406 25.2556 0 26.0594 0H30.547C31.3508 0 32.0025 0.667406 32.0025 1.49002V6.29185C33.7005 6.57706 35.3397 7.03977 36.901 7.66158L39.3342 3.74154C39.7651 3.046 40.6646 2.84132 41.343 3.28271L45.131 5.7457C45.8094 6.18708 46.01 7.10768 45.5791 7.80224L43.3158 11.4497C48.915 16.0352 52.5 23.0798 52.5 30.98C52.5 44.7986 41.531 56 28 56C14.469 56 3.5 44.7986 3.5 30.98C3.5 18.3382 12.6803 7.88761 24.604 6.19873V1.49002ZM48.1337 30.98C48.1337 42.3191 39.1298 51.5299 28 51.5299C16.8702 51.5299 7.86634 42.3191 7.86634 30.98C7.86634 19.641 16.8702 10.4302 28 10.4302C39.1298 10.4302 48.1337 19.641 48.1337 30.98ZM33.7214 20.5896C34.2959 19.8311 35.3625 19.6924 36.1037 20.2812C36.8449 20.869 36.9801 21.9613 36.4056 22.72L31.1616 29.6462C31.1969 29.7384 31.2285 29.8315 31.2562 29.9275C31.2911 30.0478 31.3199 30.171 31.3419 30.2961C31.3632 30.4164 31.3783 30.5396 31.3871 30.6638C31.393 30.7482 31.396 30.8325 31.396 30.918C31.396 32.8377 29.8756 34.3947 28 34.3947C26.1244 34.3947 24.604 32.8377 24.604 30.918C24.604 28.9982 26.1244 27.4412 28 27.4412C28.0911 27.4412 28.1814 27.4451 28.2707 27.4519L28.3984 27.4646L28.5053 27.4791L33.7214 20.5896Z"
				fill="currentColor"
				fillRule="evenodd"
			/>
		</svg>
	);
};

export default Clock;
