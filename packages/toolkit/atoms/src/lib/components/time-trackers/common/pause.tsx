import React from 'react';

const Pause = function Pause({
	ref,
	...props
}: React.SVGProps<SVGSVGElement> & {
	ref?: React.RefObject<SVGSVGElement>;
}) {
	return (
		<svg
			fill="none"
			height="56"
			ref={ref}
			viewBox="0 0 56 56"
			width="56"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<g clipPath="url(#clip0_487_3126)">
				<path
					d="M27.9996 55.2218C43.034 55.2218 55.2218 43.034 55.2218 27.9996C55.2218 12.9651 43.034 0.777344 27.9996 0.777344C12.9651 0.777344 0.777344 12.9651 0.777344 27.9996C0.777344 43.034 12.9651 55.2218 27.9996 55.2218Z"
					stroke="#AD0101"
					strokeOpacity="0.75"
					strokeWidth="2"
				/>
				<path
					d="M28.0002 52.8872C41.746 52.8872 52.8891 41.744 52.8891 27.9983C52.8891 14.2525 41.746 3.10938 28.0002 3.10938C14.2545 3.10938 3.11133 14.2525 3.11133 27.9983C3.11133 41.744 14.2545 52.8872 28.0002 52.8872Z"
					fill="#AD0101"
					fillOpacity="0.75"
				/>
				<path
					d="M27.146 41.729L26.8428 14.6213C26.8262 13.5727 27.9045 12.8075 28.8635 13.2332C29.1184 13.3484 29.3671 13.4769 29.6085 13.6182L39.7522 19.5562C43.5558 21.7828 44.607 25.8023 42.3805 29.606L36.4424 39.7497C34.2158 43.5533 31.35 43.9696 28.2455 43.1386C27.9341 43.0548 27.6584 42.8719 27.4601 42.6176C27.2618 42.3633 27.1516 42.0514 27.146 41.729ZM22.4749 40.0845L16.2489 36.4399C12.4453 34.2133 11.3941 30.1937 13.6207 26.3901L19.5587 16.2464C20.2169 15.1168 21.1677 14.1858 22.3109 13.5515C23.2556 13.0146 24.4167 13.7629 24.4317 14.8441L24.6929 38.7958C24.6956 39.0565 24.6291 39.3133 24.5002 39.54C24.3713 39.7666 24.1846 39.955 23.9592 40.0861C23.7337 40.217 23.4776 40.2859 23.2169 40.2856C22.9561 40.2854 22.7001 40.2159 22.4749 40.0845Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_487_3126">
					<rect fill="white" height="56" width="56" />
				</clipPath>
			</defs>
		</svg>
	);
};

export default Pause;
