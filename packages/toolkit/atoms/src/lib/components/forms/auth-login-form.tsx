/** @jsxImportSource theme-ui */
'use client';

import React from 'react';
import {
	Avatar,
	cn,
	Dialog,
	Popover,
	PopoverContent,
	PopoverTrigger,
	<PERSON><PERSON>,
	<PERSON><PERSON>Content,
	<PERSON><PERSON><PERSON>istThemed,
	<PERSON><PERSON><PERSON>rigger,
	ThemedButton
} from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';
// import { useLoginForm } from '@hooks/useLoginForm';
import { CheckIcon, Copy, LogOutIcon } from 'lucide-react';
import { useState } from 'react';
import { Card } from 'theme-ui';
import { useTranslation } from 'react-i18next';
import { SpinOverlayLoader } from '../loaders/spin-overlay-loader';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';
import { logOut as clocLogOut } from '@cloc/api';
import { PasswordForm } from './password-form';
import { TokenForm } from './token-form';

type FormSource = 'storybook' | 'example';

/**
 * A component to display the user's avatar with a popover menu.
 *
 * This component uses the current authenticated user's information
 * from Cloc Privider to display an avatar image. If the user is loading,
 * a loading spinner is shown. When the avatar is clicked, a popover
 * menu is displayed with further navigation options.
 *
 * @returns {React.JSX.Element | null} The user's avatar and popover menu
 * or null if the user is not authenticated.
 */

const ClocUserAvatar = ({
	children,
	showMenu = true,
	position
}: {
	children?: React.ReactNode;
	showMenu?: boolean;
	position?: 'center' | 'end' | 'start';
}) => {
	const {
		authenticatedUser: user,
		loadings: { userLoading }
	} = useClocContext();

	if (!user) return null;

	if (showMenu)
		return (
			<Popover>
				<PopoverTrigger className="outline-none px-3 relative">
					{userLoading && <SpinOverlayLoader />}
					<Avatar
						title={
							(user.firstName ? user.firstName[0] : '') + ' ' + (user.lastName ? user.lastName[0] : '')
						}
						fallback={(user.firstName ? user.firstName[0] : '') + (user.lastName ? user.lastName[0] : '')}
						src={user.imageUrl}
					/>
				</PopoverTrigger>

				<PopoverContent
					align={position}
					className="z-[1000] w-64 rounded-xl  text-sm/6 transition duration-200 ease-in-out"
				>
					<UserNavMenu>{children}</UserNavMenu>
				</PopoverContent>
			</Popover>
		);

	return (
		<Avatar
			title={(user.firstName ? user.firstName[0] : '') + ' ' + (user.lastName ? user.lastName[0] : '')}
			fallback={(user.firstName ? user.firstName[0] : '') + (user.lastName ? user.lastName[0] : '')}
			src={user.imageUrl}
		/>
	);
};

/**
 * A login form component for Cloc.
 *
 * @prop {string} [className] - The CSS class name for the component.
 * @prop {React.ForwardedRef<HTMLDivElement>} [ref] - A reference to the component.
 *
 * @example
 * <ClocLoginForm />
 *
 * @returns {JSX.Element} The login form component.
 */
const ClocLoginForm = ({
	ref,
	className,
	signupLink,
	redirectHandler
}: {
	source?: FormSource;
	className?: string;
	ref?: React.ForwardedRef<HTMLDivElement>;
	signupLink?: string;
	redirectHandler?: () => void;
}) => {
	// const { } = useClocContext();

	const { t } = useTranslation();

	return (
		<div ref={ref} className={className}>
			<div className=" pb-4 mb-4 flex flex-col gap-4 items-start justify-center border-b text-black dark:text-white   ">
				<h1 className=" text-3xl font-bold tracking-tight">{t('AUTH.sign_in_title')}</h1>
				<p className="text-xs font-light text-gray-400"></p>
			</div>
			<Tabs className="transition-all flex flex-col gap-3 delay-200" defaultValue="password">
				<TabsListThemed className="bg-primary bg-opacity-20 grid grid-cols-2 h-10 text-white">
					<TabsTrigger value="password">{t('COMMON.password')}</TabsTrigger>
					<TabsTrigger value="token">{t('COMMON.token')}</TabsTrigger>
				</TabsListThemed>
				<TabsContent value="password">
					<PasswordForm redirectHandler={redirectHandler} />
				</TabsContent>
				<TabsContent value="token">
					<TokenForm redirectHandler={redirectHandler} />
				</TabsContent>
			</Tabs>
			{signupLink && (
				<p className="text-slate-500 text-sm dark:text-white pt-3">
					{t('AUTH.dont_have_an_account')}{' '}
					<a className="text-blue-500 cursor-pointer" href={signupLink}>
						{t('AUTH.sign_up_title')}
					</a>
				</p>
			)}
			<ClocTimerFooter className="mt-2" />
		</div>
	);
};

/**
 * A component to display a login dialog.
 *
 * This component displays a dialog with a login form when the user is not authenticated.
 * If the user is authenticated, the component displays the user's avatar.
 *
 * @prop {React.ReactNode} [trigger] - The trigger element to display the dialog.
 * If not provided, a default button with 'SIGN IN' label is displayed.
 *
 * @returns {React.ReactNode} The dialog component.
 */
const ClocLoginDialog = ({
	trigger,
	signupLink,
	redirectHandler,
	position
}: {
	trigger?: React.ReactNode;
	position?: 'start' | 'center' | 'end';
	signupLink?: string;
	redirectHandler?: () => void;
}) => {
	const { authenticatedUser: user } = useClocContext();
	return (
		<div>
			{!user ? (
				<Dialog trigger={trigger ? trigger : <ThemedButton className="min-w-28">SIGN IN</ThemedButton>}>
					<ClocLoginForm redirectHandler={redirectHandler} signupLink={signupLink} />
				</Dialog>
			) : (
				<ClocUserAvatar position={position} />
			)}
		</div>
	);
};

ClocLoginForm.displayName = 'ClocLoginForm';

export function MenuIndicator() {
	return (
		<Card
			className={cn(
				'absolute top-4 -z-10 bg-transparent dark:bg-transparent ',
				'nav-items--shadow rounded-none !py-0 !px-0',
				'w-0 h-0',
				'border-l-[15px] border-r-[15px]',
				'xl:border-l-[35px] border-l-transparent xl:border-r-[35px] border-r-transparent',
				'border-solid border-b-light--theme-light dark:border-b-dark--theme-light border-b-[50px]'
			)}
		/>
	);
}

export const UserNavMenu = ({ children }: { children?: React.ReactNode }) => {
	const { authenticatedUser: user, token } = useClocContext();
	const { t } = useTranslation();

	const logOut = async () => {
		if (typeof window !== 'undefined') {
			await clocLogOut(token);

			const resetStore = {
				app: { user: null },
				persist: { token: null }
			};

			window.localStorage.setItem('_cloc-store', JSON.stringify(resetStore));
			window.location.href = '/';
		}
	};

	return (
		<div className="   text-sm text-gray-800 dark:text-gray-100">
			<div className="flex flex-col justify-center items-center gap-3 pb-3 border-b space-x-3  dark:border-gray-700">
				<Avatar
					title={
						(user!.firstName ? user!.firstName[0] : '') + ' ' + (user!.lastName ? user!.lastName[0] : '')
					}
					fallback={(user!.firstName ? user!.firstName[0] : '') + (user!.lastName ? user!.lastName[0] : '')}
					src={user!.imageUrl}
					className="size-14 rounded-full object-cover"
				/>

				<div className="flex flex-col justify-center items-center gap-1">
					<p className="font-semibold">
						{user?.firstName} {user?.lastName}
					</p>
					<p className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</p>

					<TokenDisplay token={`${token}`} />
				</div>
			</div>

			{children}

			<ul className="w-full space-y-2 border-t pt-3 dark:border-gray-700">
				{/* Logout menu */}
				<li>
					<button className="flex space-x-3 items-center font-normal text-[#DE437B]" onClick={logOut}>
						<LogOutIcon className="w-5 h-5 stroke-[#DE437B]" />{' '}
						<span>{t('AUTH.logout').toUpperCase()}</span>
					</button>
				</li>
			</ul>
		</div>
	);
};

export const TokenDisplay = ({ token }: { token: string }) => {
	const [isCopied, setIsCopied] = useState(false);
	const truncatedToken = token.length > 30 ? `${token.slice(0, 15)}...${token.slice(16, 30)}` : token;

	const handleCopy = () => {
		navigator.clipboard
			.writeText(token)
			.then(() => {
				setIsCopied(true);
				setTimeout(() => setIsCopied(false), 2000);
			})
			.catch((err) => console.error('Failed to copy: ', err));
	};

	const icon = isCopied ? <CheckIcon className="size-4" /> : <Copy className="size-4" />;

	return (
		<div className="flex gap-2 text-xs text-gray-500">
			<span>{truncatedToken}...</span>
			<button onClick={handleCopy} className="copy-button">
				{icon}
			</button>
		</div>
	);
};

export { ClocLoginForm, ClocUserAvatar, ClocLoginDialog };
