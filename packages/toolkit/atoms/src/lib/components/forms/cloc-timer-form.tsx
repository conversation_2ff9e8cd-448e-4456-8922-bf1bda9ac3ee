import { ClocTimerClientSelect } from '@components/cloc-ui-components/inputs/timer-selects/cloc-timer-client-select';
import { ClocTimerProjectSelect } from '@components/cloc-ui-components/inputs/timer-selects/cloc-timer-project-select';
import { ClocTimerTaskSelect } from '@components/cloc-ui-components/inputs/timer-selects/cloc-timer-task-select';
import { ClocTimerTeamSelect } from '@components/cloc-ui-components/inputs/timer-selects/cloc-timer-team-select';

const ClocTimerForm = ({ size }: { size?: 'default' | 'sm' | 'lg' | null }) => {
	return (
		<form className=" flex flex-col gap-3 text-black dark:text-white ">
			<ClocTimerClientSelect size={size} />
			<ClocTimerProjectSelect size={size} />
			<ClocTimerTeamSelect size={size} />
			<ClocTimerTaskSelect size={size} />
		</form>
	);
};

export { ClocTimerForm };
