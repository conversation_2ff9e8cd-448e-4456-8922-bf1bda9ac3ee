import React from 'react';
import { cn, Dialog, Input, ThemedButton } from '@cloc/ui';
import { LoaderCircle } from 'lucide-react';
import { Checkbox } from '@cloc/ui';
import { useRegistrationForm } from '@hooks/useRegistrationForm';
import { useTranslation } from 'react-i18next';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';

const ClocRegistrationForm = ({
	signInLink,
	redirectHandler,
	className
}: {
	signInLink?: string;
	redirectHandler?: () => void;
	className?: string;
}) => {
	const form = useRegistrationForm(redirectHandler);
	const { t } = useTranslation();

	return (
		<form onSubmit={form.handleSubmit} className={cn('flex flex-col gap-2 w-full ', className)}>
			<div className=" pb-4 mb-4 flex flex-col gap-4 items-start justify-center border-b text-black dark:text-white   ">
				<h1 className=" text-3xl font-bold tracking-tight">{t('AUTH.sign_up_title')}</h1>
				<p className="text-xs font-light text-gray-400"></p>
			</div>

			<label htmlFor="full-name" className="text-slate-500 dark:text-white text-sm">
				{t('COMMON.full_name')} :
			</label>
			<Input
				required
				onChange={form.handleInputChange}
				className="border"
				placeholder={t('AUTH.full_name_prompt')}
				value={form.formData.fullName}
				size={30}
				type="text"
				name="fullName"
			/>

			<label htmlFor="email" className="text-slate-500 dark:text-white text-sm">
				{t('COMMON.email')} :
			</label>
			<Input
				required
				onChange={form.handleInputChange}
				className="border"
				placeholder={t('AUTH.email_prompt')}
				value={form.formData.email}
				size={30}
				type="email"
				name="email"
			/>

			<label htmlFor="password" className="text-slate-500 dark:text-white text-sm">
				{t('COMMON.password')} :
			</label>
			<Input
				required
				onChange={form.handleInputChange}
				className="border"
				placeholder={t('AUTH.password_prompt')}
				value={form.formData.password}
				size={30}
				type="password"
				name="password"
			/>

			<label htmlFor="confirm-password" className="text-slate-500 dark:text-white text-sm">
				{t('AUTH.confirm_password_prompt')} :
			</label>
			<Input
				required
				onChange={form.handleInputChange}
				className="border"
				placeholder={t('AUTH.confirm_password_prompt')}
				value={form.formData.confirmPassword}
				size={30}
				name="confirmPassword"
				type="password"
			/>

			<div className="items-top flex space-x-2 mt-2">
				<Checkbox
					required
					id="terms"
					onCheckedChange={form.handleCheckboxChange}
					checked={form.formData.acceptTerms}
					name="acceptTerms"
				/>
				<div className="grid gap-1.5 leading-none">
					<label
						htmlFor="terms"
						className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
					>
						<p className="text-xs text-muted-foreground">
							<span>{t('COMMON.accept')} </span>
							<a
								href={'https://cloc.ai/tos'}
								className="underline"
								target={'_blank'}
								rel="noopener noreferrer"
							>
								{t('COMMON.terms_and_conditions')}
							</a>
							<span> {t('COMMON.and')} </span>
							<a href={'https://cloc.ai/privacy'} className="underline" target={'_blank'}>
								{t('COMMON.privacy_policy')}
							</a>
						</p>
					</label>
				</div>
			</div>

			{form.errors && form.errors[0] ? <span className="text-red-500 text-xs">{form.errors[0]}</span> : null}

			<ThemedButton disabled={form.loading || form.errors[0] ? true : false} className="flex gap-2 mt-5">
				{form.loading && (
					<span className=" animate-spin ">
						<LoaderCircle />
					</span>
				)}
				{t('AUTH.sign_up_title').toUpperCase()}
			</ThemedButton>

			{signInLink && (
				<p className="text-slate-500 text-sm dark:text-white pt-3">
					{t('AUTH.have_an_account')}{' '}
					<a className="text-blue-500 cursor-pointer" href={signInLink}>
						{t('AUTH.sign_in_title')}
					</a>
				</p>
			)}

			<ClocTimerFooter />
		</form>
	);
};

const ClocRegistrationDialog = ({ trigger, signInLink }: { trigger?: React.ReactNode; signInLink?: string }) => {
	return (
		<Dialog trigger={trigger ? trigger : <ThemedButton>REGISTER NOW</ThemedButton>}>
			<ClocRegistrationForm signInLink={signInLink} />
		</Dialog>
	);
};

export { ClocRegistrationForm, ClocRegistrationDialog };
