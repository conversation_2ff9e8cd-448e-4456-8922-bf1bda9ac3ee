{"name": "@cloc/atoms", "version": "0.2.8", "license": "MIT", "type": "module", "sideEffects": false, "scripts": {"build": "rollup -c", "dev": "rollup -c --watch && node post-build.js"}, "exports": {"./styles.css": "./dist/index.es.css", "./time-trackers": "./dist/lib/time-trackers/index.ts", "./universal": {"types": "./dist/universal.d.ts", "import": "./dist/universal.es.js", "require": "./dist/universal.es.js"}, "./link-adapter/next": {"types": "./dist/link-adapter.next.d.ts", "import": "./dist/link-adapter.next.js"}, "./link-adapter/remix": {"types": "./dist/link-adapter.remix.d.ts", "import": "./dist/link-adapter.remix.js"}, "./link-adapter/native": {"types": "./dist/link-adapter.native.d.ts", "import": "./dist/link-adapter.native.js"}, ".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js"}, "./libs": "./libs/index.ts", "./monorepo": {"import": "./monorepo.ts", "require": "./monorepo.ts"}}, "dependencies": {"@cloc/api": "*", "@cloc/types": "*", "@cloc/ui": "*"}, "peerDependencies": {"@emotion/react": ">=11.13.5", "next-themes": ">=0.4.4", "react": ">=18.0.0", "react-dom": ">=18.0.0", "theme-ui": ">=0.16.2"}, "optionalDependencies": {"next-themes": "^0.4.4", "@emotion/react": "^11.13.5", "theme-ui": "^0.16.2"}, "devDependencies": {"@cloc/eslint-config": "*", "@cloc/tailwind-config": "*", "@cloc/typescript-config": "*", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@rollup/plugin-json": "^6.1.0", "@types/qs": "^6", "motion": "^12.6.3", "slugify": "^1.6.6", "autoprefixer": "^10.4.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "date-fns-tz": "^3.2.0", "jotai": "^2.10.0", "lucide-react": "^0.394.0", "moment-timezone": "^0.5.46", "postcss": "^8.4.35", "qs": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "next": "15.2.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "rollup": "^4.25.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "i18next": "^24.2.2", "react-i18next": "^15.4.1"}, "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "files": ["dist"], "packageManager": "yarn@1.22.19", "publishConfig": {"access": "restricted"}, "workspaces": ["apps/*", "packages/*", "packages/toolkit/*", "packages/toolkit/examples/*"]}