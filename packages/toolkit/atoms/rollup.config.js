import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import postcss from 'rollup-plugin-postcss';
import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import terser from '@rollup/plugin-terser';
import json from '@rollup/plugin-json';
import alias from '@rollup/plugin-alias';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const createConfig = (input, outputDir, entryFileNames = '[name].es.js', preserveModules = false) => ({
	input,
	output: [
		{
			dir: outputDir,
			format: 'es',
			sourcemap: false,
			entryFileNames,
			...(preserveModules ? { preserveModules: true, preserveModulesRoot: 'src' } : {})
		}
	],
	plugins: [
		peerDepsExternal(),
		json(),
		alias({
			entries: [
				{ find: /^@components\/(.*)$/, replacement: path.resolve(__dirname, 'src/lib/components/$1') },
				{ find: /^@hooks\/(.*)$/, replacement: path.resolve(__dirname, 'src/lib/hooks/$1') },
				{ find: /^@libs\/(.*)$/, replacement: path.resolve(__dirname, 'src/lib/libs/$1') },
				{ find: /^@lib\/(.*)$/, replacement: path.resolve(__dirname, 'src/lib/$1') },
				{ find: /^@utils\/(.*)$/, replacement: path.resolve(__dirname, 'src/lib/utils/$1') },
				{
					find: '@cloc/atoms/link-adapter/next',
					replacement: path.resolve(__dirname, 'src/lib/components/universal/link-adapter.next')
				},
				{
					find: '@cloc/atoms/link-adapter/remix',
					replacement: path.resolve(__dirname, 'src/lib/components/universal/link-adapter.remix')
				},
				{
					find: '@cloc/atoms/link-adapter/native',
					replacement: path.resolve(__dirname, 'src/lib/components/universal/link-adapter.native')
				}
			]
		}),
		resolve({
			extensions: ['.js', '.ts', '.jsx', '.tsx'],
			preferBuiltins: true,
			skip: ['next/link', '@remix-run/react', 'react-router-dom']
		}),
		commonjs(),
		typescript({ tsconfig: './tsconfig.json' }),
		postcss({ minimize: true, extract: true }),
		terser({
			compress: {
				drop_console: true,
				drop_debugger: true
			},
			mangle: true
		})
	],
	watch: {
		include: 'src/**',
		exclude: 'node_modules/**'
	},
	external: [
		'react',
		'react-dom',
		'theme-ui',
		'next/link',
		'@remix-run/react',
		'@remix-run/node',
		'@remix-run/serve',
		'@remix-run/dev',
		'react-router-dom'
	],
	onwarn(warning, warn) {
		if (warning.code === 'MODULE_LEVEL_DIRECTIVE') {
			return;
		}
		if (
			warning.code === 'CIRCULAR_DEPENDENCY' &&
			(warning.message.includes('d3-interpolate') || warning.message.includes('recharts'))
		) {
			return;
		}
		warn(warning);
	}
});

export default [
	// Main index build (bundled)
	createConfig('src/index.ts', 'dist'),
	// Universal framework-agnostic build (bundled)
	createConfig('src/universal.ts', 'dist'),
	// Explicit link-adapter builds with .js extension and preserveModules
	createConfig('src/lib/components/universal/link-adapter.next.tsx', 'dist', '[name].js'),
	createConfig('src/lib/components/universal/link-adapter.remix.tsx', 'dist', '[name].js'),
	createConfig('src/lib/components/universal/link-adapter.native.tsx', 'dist', '[name].js')
];
