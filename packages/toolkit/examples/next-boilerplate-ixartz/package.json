{"name": "@cloc/example-nextjs-boilerplate", "version": "3.62.0", "author": "Ixartz (https://github.com/ixartz)", "private": "true", "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev --turbo", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "prepare": "husky"}, "dependencies": {"@arcjet/next": "^1.0.0-beta.2", "@clerk/localizations": "^3.10.5", "@clerk/nextjs": "^6.23.3", "@electric-sql/pglite": "^0.2.16", "@hookform/resolvers": "^4.0.0", "@logtail/pino": "^0.5.2", "@sentry/nextjs": "^8.54.0", "@spotlightjs/spotlight": "^2.10.3", "@t3-oss/env-nextjs": "^0.12.0", "drizzle-orm": "^0.39.3", "next": "^15.2.4", "next-intl": "^3.26.3", "pg": "^8.13.2", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "zod": "^3.24.2"}, "devDependencies": {"@antfu/eslint-config": "^4.2.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@commitlint/cz-commitlint": "^19.6.1", "@eslint-react/eslint-plugin": "^1.26.2", "@faker-js/faker": "^9.4.0", "@next/bundle-analyzer": "^15.1.7", "@next/eslint-plugin-next": "^15.1.7", "@percy/cli": "1.30.7", "@percy/playwright": "^1.0.7", "@playwright/test": "^1.50.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.5.4", "@storybook/addon-interactions": "^8.5.4", "@storybook/addon-links": "^8.5.4", "@storybook/addon-onboarding": "^8.5.4", "@storybook/blocks": "^8.5.4", "@storybook/nextjs": "^8.5.4", "@storybook/react": "^8.5.4", "@storybook/test": "^8.5.4", "@storybook/test-runner": "^0.21.0", "@tailwindcss/postcss": "^4.0.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^22.13.1", "@types/pg": "^8.11.11", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.5", "@vitest/expect": "^3.0.5", "checkly": "^4.19.1", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.30.4", "eslint": "^9.20.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-testing-library": "^7.1.1", "http-server": "^14.1.1", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "npm-run-all": "^4.1.5", "postcss": "^8.5.2", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.2", "start-server-and-test": "^2.0.10", "storybook": "^8.5.4", "tailwindcss": "^4.0.6", "typescript": "^5.7.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.5", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}