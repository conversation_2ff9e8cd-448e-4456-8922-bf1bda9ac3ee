import { getI18nPath } from '@/utils/Helpers';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import SignUp from '@/components/SignUp';

type ISignUpPageProps = {
	params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: ISignUpPageProps) {
	const { locale } = await props.params;
	const t = await getTranslations({
		locale,
		namespace: 'SignUp'
	});

	return {
		title: t('meta_title'),
		description: t('meta_description')
	};
}

export default async function SignUpPage(props: ISignUpPageProps) {
	const { locale } = await props.params;
	setRequestLocale(locale);

	return (
		// <SignUp path={getI18nPath('/sign-up', locale)} />
		<SignUp signInUrl="/dashboard" path={getI18nPath('/sign-up', locale)} />
	);
}
