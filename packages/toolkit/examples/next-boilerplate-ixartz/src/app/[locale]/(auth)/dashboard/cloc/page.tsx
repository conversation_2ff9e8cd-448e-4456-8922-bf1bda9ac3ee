import ClocShowcase from '@/components/cloc-components/showcase';
import { getTranslations } from 'next-intl/server';

type IIndexProps = {
	params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IIndexProps) {
	const { locale } = await props.params;
	const t = await getTranslations({
		locale,
		namespace: 'Cloc'
	});

	return {
		title: t('meta_title'),
		description: t('meta_description')
	};
}

const Page = () => {
	return <ClocShowcase />;
};

export default Page;
