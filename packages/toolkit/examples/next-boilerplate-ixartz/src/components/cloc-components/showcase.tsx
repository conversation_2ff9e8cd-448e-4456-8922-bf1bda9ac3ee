'use client';

import {
	ModernCloc,
	BasicTimer,
	BasicTimerBorder,
	BasicTimerBorderFullRounded,
	BasicTimerBorderRounded,
	BasicTimerContained,
	BasicTimerContainedFullRounded,
	BasicTimerContainedRounded,
	BasicTimerGray,
	BasicTimerGrayFullRounded,
	BasicTimerGrayRounded,
	BasicTimerIconGray,
	BasicTimerIconGrayFullRounded,
	BasicTimerIconGrayProgress,
	BasicTimerIconGrayProgressButton,
	BasicTimerIconGrayRounded,
	BasicTimerIconGrayRoundedProgress,
	ClocBasic,
	ClocBasicBorder,
	ClocBasicBorderRounded,
	ClocBasicBorderFullRounded,
	ClocBasicGray,
	ClocBasicGrayFullRounded,
	ClocBasicContained,
	ClocBasicContainedRounded,
	ClocBasicContainedFullRounded,
	ClocThemeToggle,
	BasicClocReport,
	ClocAppsUrlList,
	ClocProjectsList,
	ClocTasksList,
	DailyWorkedTimeDisplayer,
	WeeklyWorkedTimeDisplayer,
	WeeklyActivityDisplayer,
	DailyActivityDisplayer,
	WorkedProjectDisplayer,
	ClocRegistrationForm,
	ClocLoginForm,
	ClocTimerForm,
	ClocFontToggle
} from '@cloc/atoms';
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
	Tabs,
	TabsContent,
	TabsListThemed,
	TabsTrigger
} from '@cloc/ui';

const CarouselItems = [
	[
		<ModernCloc expanded={false} showProgress={false} size={'sm'} />,
		<ModernCloc expanded={false} showProgress={true} size={'sm'} />,
		<ModernCloc expanded={false} showProgress={true} />
	],
	[<BasicTimer />, <BasicTimerBorder />, <BasicTimerBorderFullRounded />, <BasicTimerBorderRounded />],
	[<BasicTimerContained />, <BasicTimerContainedFullRounded />, <BasicTimerContainedRounded />],
	[
		<BasicTimerGray />,
		<BasicTimerGrayFullRounded />,
		<BasicTimerGrayRounded />,
		<BasicTimerIconGray />,
		<BasicTimerIconGrayFullRounded />,
		<BasicTimerIconGrayProgress />,
		<BasicTimerIconGrayProgressButton />,
		<BasicTimerIconGrayRounded />,
		<BasicTimerIconGrayRoundedProgress />
	],
	[
		<ClocBasic />,
		<ClocBasicBorder />,
		<ClocBasicBorderRounded />,
		<ClocBasicBorderFullRounded />,
		<ClocBasicGray />,

		<ClocBasicGrayFullRounded />,
		<ClocBasicContained />,
		<ClocBasicContainedRounded />,
		<ClocBasicContainedFullRounded />
	]
];

const ClocShowcase = () => {
	return (
		<div className="flex py-3 justify-center items-center rounded-xl">
			<Tabs defaultValue="timer" className="transition-all w-full  flex flex-col  gap-3 delay-200">
				<div className="flex justify-between items-center">
					<TabsListThemed className="w-fit bg-primary place-items-center bg-opacity-20  h-10 text-white self-center">
						<TabsTrigger value="timer">{'Timers'}</TabsTrigger>
						<TabsTrigger value="report">{'Reports'}</TabsTrigger>
						<TabsTrigger value="displayer">{'Displayers'}</TabsTrigger>
						<TabsTrigger value="form">{'Forms'}</TabsTrigger>
					</TabsListThemed>
					<ClocThemeToggle />
				</div>
				<TabsContent value="timer">
					<Carousel
						opts={{
							align: 'start',
							loop: true
						}}
						className=" "
					>
						<CarouselContent>
							{CarouselItems.map((items, index) => (
								<CarouselItem
									className="flex gap-6 w-fit items-center justify-center flex-wrap"
									key={index}
								>
									{items.map((elt, i) => (
										<span key={i}>{elt}</span>
									))}
								</CarouselItem>
							))}
						</CarouselContent>
						<CarouselPrevious />
						<CarouselNext />
					</Carousel>
				</TabsContent>
				<TabsContent value="report" className="flex flex-wrap justify-center items-center gap-3">
					<BasicClocReport type="area" />
					<BasicClocReport type="line" />
					<BasicClocReport type="bar" />
					<ClocAppsUrlList />
					<ClocProjectsList />
					<ClocTasksList />
				</TabsContent>
				<TabsContent value="displayer" className="flex flex-wrap justify-center items-center gap-3">
					<DailyWorkedTimeDisplayer />
					<WeeklyWorkedTimeDisplayer />
					<WeeklyActivityDisplayer />
					<DailyActivityDisplayer />
					<WorkedProjectDisplayer />
				</TabsContent>
				<TabsContent value="form" className="flex flex-wrap justify-center items-center gap-3">
					<ClocRegistrationForm />
					<ClocLoginForm />
					<ClocTimerForm />
					<ClocFontToggle />
					<ClocThemeToggle />
				</TabsContent>
			</Tabs>
		</div>
	);
};

export default ClocShowcase;
