'use client';
import { ClocProvider } from '@cloc/atoms';
import { ThemeProvider } from 'next-themes';

export default function ClientLayout({ children, lang }: { lang?: string; children: React.ReactNode }) {
	return (
		<ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
			<ClocProvider lang={lang}>
				<main className=" dark:text-white text-black">{children}</main>
			</ClocProvider>
		</ThemeProvider>
	);
}
