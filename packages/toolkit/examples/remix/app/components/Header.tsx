import { Link } from '@remix-run/react';
import { <PERSON><PERSON><PERSON><PERSON>, ThemedButton } from '@cloc/ui';
import { ClocProvider } from '@cloc/atoms/universal';
import { useRemixClocContext } from './remix-cloc-provider';
import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

// Custom login dialog that doesn't use Radix Dialog
function CustomLoginDialog() {
	const [isOpen, setIsOpen] = useState(false);
	const [activeTab, setActiveTab] = useState('password');
	const [formData, setFormData] = useState({
		email: '',
		password: '',
		token: ''
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log('Login attempt:', formData);
		// Add your login logic here
	};

	const handleInputChange = (field: string, value: string) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	const handleTabKeyDown = (e: React.KeyboardEvent, tab: string) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			setActiveTab(tab);
		}
	};

	// Close modal when clicking outside
	const handleBackdropClick = (e: React.MouseEvent) => {
		if (e.target === e.currentTarget) {
			setIsOpen(false);
		}
	};

	// Close modal on backdrop key press
	const handleBackdropKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Escape') {
			setIsOpen(false);
		}
	};

	// Close modal on Escape key
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === 'Escape' && isOpen) {
				setIsOpen(false);
			}
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
			// Prevent body scroll
			document.body.style.overflow = 'hidden';
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
			document.body.style.overflow = 'unset';
		};
	}, [isOpen]);

	const modal = (
		<button
			className="fixed inset-0 z-[var(--z-overlay)] flex items-center justify-center min-h-screen py-8"
			onClick={handleBackdropClick}
			onKeyDown={handleBackdropKeyDown}
			aria-label="Close dialog"
			style={{
				backgroundColor: 'rgba(0, 0, 0, 0.5)',
				backdropFilter: 'blur(2px)'
			}}
		>
			<div
				className="bg-white dark:bg-gray-900 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md mx-4"
				role="dialog"
				aria-modal="true"
				aria-labelledby="dialog-title"
			>
				{/* Header */}
				<div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200 dark:border-gray-700">
					<h1 id="dialog-title" className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
						Sign In
					</h1>
					<button
						onClick={() => setIsOpen(false)}
						className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
					>
						<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
							<path d="M15.7128 16.7716L7.22748 8.2863C6.93757 7.99639 6.93757 7.51556 7.22748 7.22564C7.5174 6.93573 7.99823 6.93573 8.28814 7.22564L16.7734 15.7109C17.0633 16.0008 17.0633 16.4817 16.7734 16.7716C16.4835 17.0615 16.0027 17.0615 15.7128 16.7716Z" />
							<path d="M7.22658 16.7716C6.93666 16.4817 6.93666 16.0008 7.22658 15.7109L15.7119 7.22564C16.0018 6.93573 16.4826 6.93573 16.7725 7.22564C17.0624 7.51556 17.0624 7.99639 16.7725 8.2863L8.28724 16.7716C7.99732 17.0615 7.51649 17.0615 7.22658 16.7716Z" />
						</svg>
					</button>
				</div>

				{/* Tab Navigation */}
				<div className="flex bg-blue-600 bg-opacity-20 rounded-lg p-1 mb-4" role="tablist">
					<button
						onClick={() => setActiveTab('password')}
						role="tab"
						aria-selected={activeTab === 'password'}
						aria-controls="password-panel"
						tabIndex={activeTab === 'password' ? 0 : -1}
						className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${activeTab === 'password'
							? 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white shadow'
							: 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
							}`}
						onKeyDown={(e) => handleTabKeyDown(e, 'password')}
					>
						Password
					</button>
					<button
						onClick={() => setActiveTab('token')}
						role="tab"
						aria-selected={activeTab === 'token'}
						aria-controls="token-panel"
						tabIndex={activeTab === 'token' ? 0 : -1}
						className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${activeTab === 'token'
							? 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white shadow'
							: 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
							}`}
						onKeyDown={(e) => handleTabKeyDown(e, 'token')}
					>
						Token
					</button>
				</div>

				{/* Form */}
				<form onSubmit={handleSubmit} className="space-y-4">
					<div role="tabpanel" id="password-panel" aria-labelledby="password-tab" hidden={activeTab !== 'password'}>
						{activeTab === 'password' && (
							<>
								<div>
									<label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
										Email:
									</label>
									<input
										type="email"
										id="email"
										value={formData.email}
										onChange={(e) => handleInputChange('email', e.target.value)}
										placeholder="Enter your email"
										required
										className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
										Password:
									</label>
									<input
										type="password"
										id="password"
										value={formData.password}
										onChange={(e) => handleInputChange('password', e.target.value)}
										placeholder="Enter your password"
										required
										className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
									/>
								</div>
							</>
						)}
					</div>
					<div role="tabpanel" id="token-panel" aria-labelledby="token-tab" hidden={activeTab !== 'token'}>
						{activeTab === 'token' && (
							<div>
								<label htmlFor="token" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Token:
								</label>
								<input
									type="text"
									id="token"
									value={formData.token}
									onChange={(e) => handleInputChange('token', e.target.value)}
									placeholder="Enter your token"
									required
									className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
								/>
							</div>
						)}
					</div>

					<ThemedButton
						type="submit"
						className="w-full"
					>
						SIGN IN
					</ThemedButton>
				</form>

				{/* Footer */}
				<div className="flex justify-center items-center mt-4 text-xs text-gray-500 dark:text-gray-400">
					<h1>Powered by{' '}
						<a
							href="https://cloc.ai/"
							target="_blank"
							rel="noopener noreferrer"
							className="font-bold underline hover:text-blue-600 dark:hover:text-blue-400"
						>
							Ever Cloc
						</a>
					</h1>
				</div>
			</div>
		</button>
	);

	const portalEl = typeof window !== 'undefined'
		? document.getElementById('modal-portal')
		: null;

	return (
		<>
			<ThemedButton
				onClick={() => setIsOpen(true)}
				className="min-w-28"
			>
				SIGN IN
			</ThemedButton>
			{isOpen && portalEl
				? createPortal(modal, portalEl)
				: null}
		</>
	);
}

function ThemeToggle() {
	const { theme, setTheme } = useRemixClocContext();

	return (
		<div className="flex justify-center items-center gap-3 rounded-full bg-[#E7E7EA] dark:bg-[#25272D] p-2">
			<button
				className={`${theme === 'light' ? 'bg-[#050011]' : 'bg-transparent'} rounded-2xl`}
				onClick={() => setTheme('light')}
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="30"
					height="30"
					viewBox="0 0 30 30"
					id="sun-svg"
					fill="currentColor"
				>
					<g
						id="sun"
						transform="translate(0)"
						style={{
							fill: '#fff'
						}}
					>
						<g transform="translate(4.662 4.616)">
							<path
								d="M.708,0A.708.708,0,0,0,0,.708V2.721a.708.708,0,1,0,1.416,0V.708A.708.708,0,0,0,.708,0Z"
								transform="translate(9.665 17.319)"
							/>
							<path
								d="M.708,3.429a.708.708,0,0,0,.708-.708V.708A.708.708,0,0,0,0,.708V2.721A.708.708,0,0,0,.708,3.429Z"
								transform="translate(9.665 0)"
							/>
							<path
								d="M1.632.207.208,1.63a.708.708,0,0,0,1,1L2.633,1.209a.708.708,0,0,0-1-1Z"
								transform="translate(2.831 15.078)"
							/>
							<path
								d="M.708,2.839a.706.706,0,0,0,.5-.207L2.632,1.209a.708.708,0,1,0-1-1L.207,1.631a.708.708,0,0,0,.5,1.209Z"
								transform="translate(15.077 2.831)"
							/>
							<path
								d="M3.429.708A.708.708,0,0,0,2.721,0H.708a.708.708,0,0,0,0,1.416H2.721A.708.708,0,0,0,3.429.708Z"
								transform="translate(0 9.666)"
							/>
							<path
								d="M2.722,0H.708a.708.708,0,0,0,0,1.416H2.722A.708.708,0,1,0,2.722,0Z"
								transform="translate(17.319 9.666)"
							/>
							<path
								d="M1.631,2.632a.708.708,0,1,0,1-1L1.209.207a.708.708,0,0,0-1,1Z"
								transform="translate(2.832 2.831)"
							/>
							<path
								d="M1.209.207a.708.708,0,0,0-1,1L1.63,2.632a.708.708,0,1,0,1-1Z"
								transform="translate(15.078 15.078)"
							/>
							<path
								d="M0,5.511a5.511,5.511,0,1,1,5.511,5.511A5.517,5.517,0,0,1,0,5.511Z"
								transform="translate(4.863 4.863)"
							/>
						</g>
					</g>
				</svg>
			</button>
			<button
				className={`${theme === 'dark' ? 'bg-[#6a7c90]' : 'bg-transparent'} rounded-2xl border-none`}
				onClick={() => setTheme('dark')}
			>
				<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="currentColor">
					<g id="moon" transform="translate(7.576 7.576)">
						<path
							d="M13.642,8.666a1.1,1.1,0,0,0-1-.442,6.077,6.077,0,0,1-.788.031A5.836,5.836,0,0,1,6.117,2.546,5.759,5.759,0,0,1,6.235,1.29,1.069,1.069,0,0,0,5.88.263,1.1,1.1,0,0,0,4.8.061,7.1,7.1,0,0,0,0,6.844a6.994,6.994,0,0,0,2.143,4.944A7.223,7.223,0,0,0,7.19,13.846h.019a7.265,7.265,0,0,0,3.914-1.136,7.142,7.142,0,0,0,2.62-2.959A1.068,1.068,0,0,0,13.642,8.666Zm-6.434,3.89H7.193A5.878,5.878,0,0,1,1.311,6.828,5.8,5.8,0,0,1,4.883,1.415,7,7,0,0,0,6.895,7.437,7.225,7.225,0,0,0,11.82,9.543c.188,0,.377,0,.565-.007A5.938,5.938,0,0,1,7.208,12.556Z"
							transform="translate(0 0)"
						/>
					</g>
				</svg>
			</button>
		</div>
	);
}

export function Header() {
	return (
		<div className="w-full fixed top-0 left-0 right-0 z-50 px-4 py-3">
			<header className="max-w-7xl mx-auto bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-100 dark:border-gray-800/50">
				<div className="px-6 mx-auto">
					<div className="flex justify-between items-center h-16">
						{/* Left section - Logo and Navigation */}
						<div className="flex items-center gap-8">
							<Link to="/" prefetch="intent" className="flex items-center gap-2">
								<ClocLogo className="h-6 w-auto" />
							</Link>

							<nav className="flex items-center">
								<Link
									to="/"
									prefetch="intent"
									className="px-4 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
								>
									Home
								</Link>
								<Link
									to="/components"
									prefetch="intent"
									className="px-4 py-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors font-medium"
								>
									Components
								</Link>
							</nav>
						</div>

						{/* Right section - Login Dialog and Theme Toggle */}
						<div className="flex items-center gap-4">
							<ClocProvider>
								<ThemeToggle />
								<CustomLoginDialog />
							</ClocProvider>
						</div>
					</div>
				</div>
			</header>
		</div>
	);
}
