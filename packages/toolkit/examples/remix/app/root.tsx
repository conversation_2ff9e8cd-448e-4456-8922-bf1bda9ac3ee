import {
  <PERSON><PERSON>,
  <PERSON><PERSON>eload,
  Met<PERSON>,
  <PERSON>let,
  <PERSON><PERSON>ts,
  ScrollRestoration,
} from "@remix-run/react";
import type { LinksFunction } from "@remix-run/node";
import { RemixClocProvider } from "./components/remix-cloc-provider";
import { Header } from "./components/Header";
import { Footer } from "./components/Footer";
import styles from "./tailwind.css";
import atomsStyles from "./styles/atoms.css";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap",
  },
  { rel: "stylesheet", href: styles },
  { rel: "stylesheet", href: atomsStyles },
];

export default function App() {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              let theme = localStorage.getItem('theme');
              if (!theme) {
                theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
              }
              if (theme === 'dark') {
                document.documentElement.classList.add('dark');
              } else {
                document.documentElement.classList.remove('dark');
              }
            `,
          }}
        />
      </head>
      <body className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Portal container for modals */}
        <div id="modal-portal" style={{ position: 'fixed', zIndex: 'var(--z-modal)', inset: 0, width: '100vw', height: '100vh' }} />
        <RemixClocProvider>
          <Header />
          <main className="pt-24">
            <Outlet />
          </main>
          <Footer />
          <ScrollRestoration />
          <Scripts />
          <script
            dangerouslySetInnerHTML={{
              __html: `window.process = { env: { NODE_ENV: 'development' } };`,
            }}
          />
          <LiveReload />
        </RemixClocProvider>
      </body>
    </html>
  );
}
