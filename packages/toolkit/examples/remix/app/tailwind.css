@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --z-modal: 99999;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  /* Ensure main element doesn't create a stacking context that covers modals */
  main {
    z-index: 1 !important;
    position: relative !important;
    transform: none !important;
    filter: none !important;
    will-change: auto !important;
  }

  /* Fix body pointer-events when dialog is open */
  body[data-scroll-locked="1"] {
    pointer-events: auto !important;
  }

  /* Ensure body maintains pointer-events for dialog interactions */
  body:has([data-radix-dialog-content][data-state="open"]) {
    pointer-events: auto !important;
  }

  /* Alternative approach for browsers that don't support :has() */
  body[style*="pointer-events: none"] {
    pointer-events: auto !important;
  }
}

@layer utilities {

  /* Ensure modals are always on top with maximum specificity */
  [role="dialog"],
  [data-radix-dialog-content],
  [data-radix-dialog-overlay],
  [data-radix-portal] {
    z-index: 99999 !important;
    position: fixed !important;
  }

  /* Ensure modal overlays are visible */
  [data-radix-dialog-overlay] {
    position: fixed !important;
    inset: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: var(--z-modal) !important;
    pointer-events: auto !important;
    box-sizing: border-box !important;
  }

  /* Force modal portal to be rendered at body level */
  [data-radix-portal] {
    z-index: var(--z-portal) !important;
    position: fixed !important;
  }

  /* Ensure DialogContent is properly positioned */
  [data-radix-dialog-content] {
    position: fixed !important;
    z-index: 99999 !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  /* Override any conflicting z-index values */
  .z-50 {
    z-index: 99999 !important;
  }

  /* Additional specificity for Radix Dialog elements */
  body [data-radix-dialog-content] {
    z-index: 99999 !important;
  }

  body [data-radix-dialog-overlay] {
    z-index: 99998 !important;
  }

  /* Force all dialog-related elements to be on top */
  div[role="dialog"],
  div[data-radix-dialog-content],
  div[data-radix-dialog-overlay] {
    z-index: 99999 !important;
  }

  /* Ensure dialog content has pointer-events */
  [data-radix-dialog-content] {
    pointer-events: auto !important;
  }

  /* Ensure dialog overlay has pointer-events */
  [data-radix-dialog-overlay] {
    pointer-events: auto !important;
  }
}

/* Force modal visibility - highest specificity */
html body [data-radix-dialog-content] {
  z-index: 99999 !important;
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

html body [data-radix-dialog-overlay] {
  z-index: 99998 !important;
  position: fixed !important;
  inset: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* Override any transform or filter that might hide the modal */
html body [data-radix-dialog-content] {
  transform: translate(-50%, -50%) !important;
  filter: none !important;
  backdrop-filter: none !important;
}

html body [data-radix-dialog-overlay] {
  transform: none !important;
  filter: none !important;
  backdrop-filter: none !important;
}

/* Ensure the modal is not clipped by any parent */
html body [data-radix-dialog-content] {
  clip: auto !important;
  clip-path: none !important;
  overflow: visible !important;
}

/* Specific override for Radix Dialog z-50 class */
html body .z-50[data-radix-dialog-content],
html body .z-50[data-radix-dialog-overlay] {
  z-index: 99999 !important;
}

/* Override any Radix Dialog specific styles */
html body [data-radix-dialog-content].z-50 {
  z-index: 99999 !important;
}

html body [data-radix-dialog-overlay].z-50 {
  z-index: 99998 !important;
}

/* Force all elements with z-50 to be overridden for dialogs */
html body *[class*="z-50"][data-radix-dialog-content] {
  z-index: 99999 !important;
}

html body *[class*="z-50"][data-radix-dialog-overlay] {
  z-index: 99998 !important;
}

/* Critical fix for body pointer-events */
html body[data-scroll-locked="1"] {
  pointer-events: auto !important;
}

html body[style*="pointer-events: none"] {
  pointer-events: auto !important;
}

/* Ensure dialog content and overlay are interactive */
html body [data-radix-dialog-content],
html body [data-radix-dialog-overlay] {
  pointer-events: auto !important;
}

html,
body {
  height: 100vh !important;
  min-height: 100vh !important;
  width: 100vw !important;
  min-width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
  position: static !important;
  overflow-x: hidden !important;
}
