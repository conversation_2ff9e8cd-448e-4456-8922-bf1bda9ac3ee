/**
 * This is intended to be a basic starting point for linting in your app.
 * It relies on recommended configs out of the box for simplicity, but you can
 * and should modify this configuration to best suit your team's needs.
 */

/** @type {import('eslint').Linter.Config} */
module.exports = {
	root: true,
	parserOptions: {
		ecmaVersion: 'latest',
		sourceType: 'module',
		ecmaFeatures: {
			jsx: true
		}
	},
	env: {
		browser: true,
		commonjs: true,
		es6: true
	},
	ignorePatterns: ['!**/.server', '!**/.client'],

	// Base config
	extends: ['eslint:recommended'],

	overrides: [
		// React
		{
			files: ['**/*.{js,jsx,ts,tsx}'],
			plugins: ['react', 'jsx-a11y'],
			extends: [
				'plugin:react/recommended',
				'plugin:react/jsx-runtime',
				'plugin:react-hooks/recommended',
				'plugin:jsx-a11y/recommended'
			],
			settings: {
				react: {
					version: 'detect'
				},
				formComponents: ['Form'],
				linkComponents: [
					{ name: 'Link', linkAttribute: 'to' },
					{ name: 'NavLink', linkAttribute: 'to' }
				]
			}
		},

		// Typescript
		{
			files: ['**/*.{ts,tsx}'],
			plugins: ['@typescript-eslint', 'import'],
			parser: '@typescript-eslint/parser',
			settings: {
				'import/internal-regex': '^~/',
				'import/resolver': {
					node: {
						extensions: ['.ts', '.tsx'],
						moduleDirectory: ['node_modules', '../../node_modules']
					},
					typescript: {
						alwaysTryTypes: true,
						project: './tsconfig.json'
					}
				},
				'import/parsers': {
					'@typescript-eslint/parser': ['.ts', '.tsx']
				}
			},
			extends: ['plugin:@typescript-eslint/recommended', 'plugin:import/recommended', 'plugin:import/typescript'],
			rules: {
				'import/no-unresolved': 'error',
				'import/named': 'error',
				'import/default': 'error',
				'import/namespace': 'error'
			}
		},

		// Node
		{
			files: ['.eslintrc.cjs'],
			env: {
				node: true
			}
		}
	]
};
