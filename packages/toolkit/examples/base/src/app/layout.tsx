import { Inter as FontSans, Plus_Jakarta_Sans, Inter } from 'next/font/google';
import './globals.css';
import { Metadata } from 'next';
import ClientLayout from './client-layout';
import './prism-custom.css';
const inter = Inter({
	subsets: ['latin'],
	fallback: [
		'-apple-system',
		'BlinkMacSystemFont',
		'Segoe UI',
		'Roboto',
		'Oxygen',
		'Ubuntu',
		'Cantarell',
		'Fira Sans',
		'Droid Sans',
		'Helvetica Neue',
		'sans-serif'
	]
});
const plus_jakarta_sans = Plus_Jakarta_Sans({
	subsets: ['latin'],
	fallback: [
		'-apple-system',
		'BlinkMacSystemFont',
		'Segoe UI',
		'Roboto',
		'Oxygen',
		'Ubuntu',
		'Cantarell',
		'Fira Sans',
		'Droid Sans',
		'Helvetica Neue',
		'sans-serif'
	]
});
const fontSans = FontSans({
	subsets: ['latin'],
	variable: '--font-sans'
});

export const metadata: Metadata = {
	title: 'Cloc | Examples',
	description: 'Components examples of Cloc'
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body className={`${fontSans.variable} font-sans antialiased`}>
				<ClientLayout>{children}</ClientLayout>
			</body>
		</html>
	);
}
