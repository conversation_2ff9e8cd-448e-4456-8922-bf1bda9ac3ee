'use client';

import { PageTitle } from '@/components/page-title';
import {
	Basic<PERSON>locReport,
	ClocAppsUrlList,
	ClocProjectDisplayer,
	ClocReportDisplayer,
	DailyActivityDisplayer,
	DailyWorkedTimeDisplayer,
	WeeklyActivityDisplayer,
	WeeklyWorkedTimeDisplayer,
	WorkedProjectDisplayer
} from '@cloc/atoms';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function DisplayerPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Displayers reports" description="Displayer Components in Cloc." />

			<div className="flex flex-wrap   items-start gap-4">
				<ComponentExample
					title="Worked Project Displayer"
					code={`import { WorkedProjectDisplayer } from '@cloc/atoms';

<WorkedProjectDisplayer />`}
				>
					<WorkedProjectDisplayer />
				</ComponentExample>
				<ComponentExample
					title="Weekly Worked Time Displayer"
					code={`import { WeeklyWorkedTimeDisplayer } from '@cloc/atoms';

<WeeklyWorkedTimeDisplayer />`}
				>
					<WeeklyWorkedTimeDisplayer />
				</ComponentExample>
				<ComponentExample
					title="Daily Worked Time Displayer"
					code={`import { DailyWorkedTimeDisplayer } from '@cloc/atoms';

<DailyWorkedTimeDisplayer />`}
				>
					<DailyWorkedTimeDisplayer />
				</ComponentExample>

				<ComponentExample
					title="Daily Activity Displayer"
					code={`import { DailyActivityDisplayer } from '@cloc/atoms';

<DailyActivityDisplayer />`}
				>
					<DailyActivityDisplayer />
				</ComponentExample>

				<ComponentExample
					title="Weekly Activity Displayer"
					code={`import { WeeklyActivityDisplayer } from '@cloc/atoms';

<WeeklyActivityDisplayer />`}
				>
					<WeeklyActivityDisplayer />
				</ComponentExample>
			</div>
		</div>
	);
}
