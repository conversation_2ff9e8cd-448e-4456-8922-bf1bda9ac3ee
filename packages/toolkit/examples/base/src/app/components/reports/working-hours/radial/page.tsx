'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocReport } from '@cloc/atoms';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function RadialChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Radial Charts" description="Collection of Radial Charts in Cloc." />

			<Tabs defaultValue="radial" className="w-full">
				<TabsList>
					<TabsTrigger value="radial">Radial Chart</TabsTrigger>
					<TabsTrigger value="radar">Radar Chart</TabsTrigger>
				</TabsList>

				<TabsContent value="radial" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Radial Charts</h2>
					<div className="grid grid-cols-1 gap-4">
						<ComponentExample
							title="Default Radial Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="radial" />`}
						>
							<BasicClocReport type="radial" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Radial Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="radial" variant="bordered" />`}
						>
							<BasicClocReport type="radial" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>

				<TabsContent value="radar" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Radar Charts</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<ComponentExample
							title="Default Radar Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="radar" />`}
						>
							<BasicClocReport type="radar" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Radar Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="radar" variant="bordered" />`}
						>
							<BasicClocReport type="radar" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
