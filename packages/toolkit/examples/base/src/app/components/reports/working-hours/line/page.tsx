'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocReport } from '@cloc/atoms';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function LineChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Line Charts" description="Collection of Line Charts Components in Cloc." />

			<Tabs defaultValue="line" className="w-full">
				<TabsList>
					<TabsTrigger value="line">Line Chart</TabsTrigger>
					<TabsTrigger value="area">Area Chart</TabsTrigger>
					<TabsTrigger value="tooltip">Tooltip Chart</TabsTrigger>
				</TabsList>

				<TabsContent value="line" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Line Charts</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<ComponentExample
							title="Default Line Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="line" />`}
						>
							<BasicClocReport type="line" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Line Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="line" variant="bordered" />`}
						>
							<BasicClocReport type="line" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>

				<TabsContent value="area" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Area Charts</h2>
					<div className="grid grid-cols-1 gap-4">
						<ComponentExample
							title="Default Area Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="area" />`}
						>
							<BasicClocReport type="area" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Area Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="area" variant="bordered" />`}
						>
							<BasicClocReport type="area" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>

				<TabsContent value="tooltip" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Tooltip Charts</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<ComponentExample
							title="Default Tooltip Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="tooltip" />`}
						>
							<BasicClocReport type="tooltip" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Tooltip Chart"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="tooltip" variant="bordered" />`}
						>
							<BasicClocReport type="tooltip" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
