'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocReport, ClocAppsUrlList, ClocProjectsList } from '@cloc/atoms';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function BarChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Worked Projects reports" description="Worked Projects List Component in Cloc." />

			<div className="grid grid-cols-1 gap-4">
				<ComponentExample
					title="Default Component"
					code={`import { ClocProjectsList } from '@cloc/atoms';

<ClocProjectsList />`}
				>
					<ClocProjectsList />
				</ComponentExample>
			</div>
		</div>
	);
}
