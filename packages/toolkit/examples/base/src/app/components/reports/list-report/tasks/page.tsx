'use client';

import { PageTitle } from '@/components/page-title';
import { Basic<PERSON>locReport, ClocAppsUrlList, ClocTasksList } from '@cloc/atoms';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function BarChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Tasks List reports" description="Tasks List Report Component in Cloc." />

			<div className="grid grid-cols-1 gap-4">
				<ComponentExample
					title="Component : ClocTasksList"
					code={`import { ClocTasksList } from '@cloc/atoms';

<ClocTasksList />`}
				>
					<ClocTasksList />
				</ComponentExample>
			</div>
		</div>
	);
}
