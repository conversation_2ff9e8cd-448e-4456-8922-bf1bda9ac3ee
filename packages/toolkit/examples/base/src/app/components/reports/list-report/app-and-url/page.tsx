'use client';

import { PageTitle } from '@/components/page-title';
import { Basic<PERSON>locReport, ClocAppsUrlList } from '@cloc/atoms';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function BarChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Apps and Url reports" description=" Apps and Urls List Component in Cloc." />

			<div className="grid grid-cols-1 gap-4">
				<ComponentExample
					title="Component : ClocAppsUrlList"
					code={`import { ClocAppsUrlList } from '@cloc/atoms';

<ClocAppsUrlList />`}
				>
					<ClocAppsUrlList />
				</ComponentExample>
			</div>
		</div>
	);
}
