'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocReport } from '@cloc/atoms';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@cloc/ui';
import { ComponentExample } from '@/components/component-example';

export default function BarChartsPage() {
	return (
		<div className="space-y-8">
			<PageTitle title="Bar Charts" description="Collection of Bar Charts Components in Cloc." />

			<Tabs defaultValue="vertical" className="w-full">
				<TabsList>
					<TabsTrigger value="vertical">Vertical Bar</TabsTrigger>
					<TabsTrigger value="horizontal">Horizontal Bar</TabsTrigger>
				</TabsList>

				<TabsContent value="vertical" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Vertical Bar Charts</h2>
					<div className="grid grid-cols-1 gap-4">
						<ComponentExample
							title="Default Vertical Bar"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="bar-vertical" />`}
						>
							<BasicClocReport type="bar-vertical" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Vertical Bar"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="bar-vertical" variant="bordered" />`}
						>
							<BasicClocReport type="bar-vertical" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>

				<TabsContent value="horizontal" className="space-y-4">
					<h2 className="text-xl text-slate-500 dark:text-slate-400">Horizontal Bar Charts</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<ComponentExample
							title="Default Horizontal Bar"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="bar" />`}
						>
							<BasicClocReport type="bar" />
						</ComponentExample>

						<ComponentExample
							title="Bordered Horizontal Bar"
							code={`import { BasicClocReport } from '@cloc/atoms';

<BasicClocReport type="bar" variant="bordered" />`}
						>
							<BasicClocReport type="bar" variant="bordered" />
						</ComponentExample>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
