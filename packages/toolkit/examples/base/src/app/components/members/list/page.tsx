'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocMember } from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

const mockMembers = [
  { label: '<PERSON>', progress: 75, color: '#4CAF50' },
  { label: '<PERSON>', progress: 45, color: '#2196F3' },
  { label: '<PERSON>', progress: 90, color: '#9C27B0' },
  { label: '<PERSON>', progress: 60, color: '#FF9800' },
];

export default function MemberList() {
  return (
    <div className="space-y-8">
      <PageTitle 
        title="Member List Components"
        description="Collection of Member List Components in Cloc."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
        <ComponentExample
          title="Default List"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMembers = [
  { label: '<PERSON>', progress: 75, color: '#4CAF50' },
  { label: '<PERSON>', progress: 45, color: '#2196F3' },
  { label: '<PERSON>', progress: 90, color: '#9C27B0' },
  { label: '<PERSON>', progress: 60, color: '#FF9800' },
];

<BasicClocMember 
  values={mockMembers}
  showProgress
  title="Team Members"
/>`}
        >
          <BasicClocMember 
            values={mockMembers}
            showProgress
            title="Team Members"
          />
        </ComponentExample>

        <ComponentExample
          title="With Time"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMembers = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' },
  { label: 'Jane Smith', progress: 45, color: '#2196F3' },
  { label: 'Bob Johnson', progress: 90, color: '#9C27B0' },
  { label: 'Alice Brown', progress: 60, color: '#FF9800' },
];

<BasicClocMember 
  values={mockMembers}
  showTime
  title="Team Activity"
/>`}
        >
          <BasicClocMember 
            values={mockMembers}
            showTime
            title="Team Activity"
          />
        </ComponentExample>

        <ComponentExample
          title="Progress & Time"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMembers = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' },
  { label: 'Jane Smith', progress: 45, color: '#2196F3' },
  { label: 'Bob Johnson', progress: 90, color: '#9C27B0' },
  { label: 'Alice Brown', progress: 60, color: '#FF9800' },
];

<BasicClocMember 
  values={mockMembers}
  showProgress
  showTime
  title="Full Activity"
/>`}
        >
          <BasicClocMember 
            values={mockMembers}
            showProgress
            showTime
            title="Full Activity"
          />
        </ComponentExample>

        <ComponentExample
          title="Bordered Variant"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMembers = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' },
  { label: 'Jane Smith', progress: 45, color: '#2196F3' },
  { label: 'Bob Johnson', progress: 90, color: '#9C27B0' },
  { label: 'Alice Brown', progress: 60, color: '#FF9800' },
];

<BasicClocMember 
  values={mockMembers}
  showProgress
  showTime
  variant="bordered"
  title="Bordered List"
/>`}
        >
          <BasicClocMember 
            values={mockMembers}
            showProgress
            showTime
            variant="bordered"
            title="Bordered List"
          />
        </ComponentExample>
      </div>
    </div>
  );
}