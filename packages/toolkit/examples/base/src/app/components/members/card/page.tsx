'use client';

import { PageTitle } from '@/components/page-title';
import { BasicClocMember } from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

const mockMember = [
  { label: '<PERSON>', progress: 75, color: '#4CAF50' }
];

export default function MemberCard() {
  return (
    <div className="space-y-8">
      <PageTitle 
        title="Member Card Components"
        description="Collection of Member Card Components in Cloc."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <ComponentExample
          title="Default Card"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMember = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' }
];

<BasicClocMember 
  values={mockMember}
  size="sm"
  title="Member Card"
/>`}
        >
          <BasicClocMember 
            values={mockMember}
            size="sm"
            title="Member Card"
          />
        </ComponentExample>

        <ComponentExample
          title="With Progress"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMember = [
  { label: '<PERSON>e', progress: 75, color: '#4CAF50' }
];

<BasicClocMember 
  values={mockMember}
  size="sm"
  showProgress
  title="Activity Card"
/>`}
        >
          <BasicClocMember 
            values={mockMember}
            size="sm"
            showProgress
            title="Activity Card"
          />
        </ComponentExample>

        <ComponentExample
          title="With Time"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMember = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' }
];

<BasicClocMember 
  values={mockMember}
  size="sm"
  showTime
  title="Time Card"
/>`}
        >
          <BasicClocMember 
            values={mockMember}
            size="sm"
            showTime
            title="Time Card"
          />
        </ComponentExample>

        <ComponentExample
          title="Full Info"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMember = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' }
];

<BasicClocMember 
  values={mockMember}
  size="sm"
  showProgress
  showTime
  title="Full Info Card"
/>`}
        >
          <BasicClocMember 
            values={mockMember}
            size="sm"
            showProgress
            showTime
            title="Full Info Card"
          />
        </ComponentExample>

        <ComponentExample
          title="Bordered"
          code={`import { BasicClocMember } from '@cloc/atoms';

const mockMember = [
  { label: 'John Doe', progress: 75, color: '#4CAF50' }
];

<BasicClocMember 
  values={mockMember}
  size="sm"
  showProgress
  showTime
  variant="bordered"
  title="Bordered Card"
/>`}
        >
          <BasicClocMember 
            values={mockMember}
            size="sm"
            showProgress
            showTime
            variant="bordered"
            title="Bordered Card"
          />
        </ComponentExample>
      </div>
    </div>
  );
}