'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@cloc/ui";
import { BasicVariants } from './variants/basic-variants';
import { IconVariants } from './variants/icon-variants';
import { ProgressVariants } from './variants/progress-variants';
import { ButtonVariants } from './variants/button-variants';
import { PageTitle } from "@/components/page-title";

export default function ClocBaseTimerPage() {
  return (
      <div className="space-y-8">
        <PageTitle 
          title="Cloc Base Timer Components"
          description="Collection of Cloc Base Timer Components for building Cloc applications."
        />
        
        <Tabs defaultValue="basic" className="w-full">
          <TabsList>
            <TabsTrigger value="basic">Basic Variants</TabsTrigger>
            <TabsTrigger value="icons">With Icons</TabsTrigger>
            <TabsTrigger value="progress">With Progress</TabsTrigger>
            <TabsTrigger value="buttons">With Buttons</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <BasicVariants />
          </TabsContent>

          <TabsContent value="icons">
            <IconVariants />
          </TabsContent>

          <TabsContent value="progress">
            <ProgressVariants />
          </TabsContent>

          <TabsContent value="buttons">
            <ButtonVariants />
          </TabsContent>
        </Tabs>
      </div>
  );
}