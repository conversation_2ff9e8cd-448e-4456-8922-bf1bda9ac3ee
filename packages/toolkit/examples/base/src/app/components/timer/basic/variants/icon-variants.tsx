'use client';

import {
  BasicTimerIcon,
  BasicTimerIconBorder,
  BasicTimerIconBorderRounded,
  BasicTimerIconBorderFullRounded,
  BasicTimerIconGray,
  BasicTimerIconGrayRounded,
  BasicTimerIconGrayFullRounded,
  BasicTimerIconContained,
  BasicTimerIconContainedRounded,
  BasicTimerIconContainedFullRounded
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function IconVariants() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <ComponentExample
        title="Default with Icon"
        code={`import { BasicTimerIcon } from '@cloc/atoms';

<BasicTimerIcon />`}
      >
        <BasicTimerIcon />
      </ComponentExample>

      <ComponentExample
        title="Border with Icon"
        code={`import { BasicTimerIconBorder } from '@cloc/atoms';

<BasicTimerIconBorder />`}
      >
        <BasicTimerIconBorder />
      </ComponentExample>

      <ComponentExample
        title="Border Rounded with Icon"
        code={`import { BasicTimerIconBorderRounded } from '@cloc/atoms';

<BasicTimerIconBorderRounded />`}
      >
        <BasicTimerIconBorderRounded />
      </ComponentExample>

      <ComponentExample
        title="Border Full Rounded with Icon"
        code={`import { BasicTimerIconBorderFullRounded } from '@cloc/atoms';

<BasicTimerIconBorderFullRounded />`}
      >
        <BasicTimerIconBorderFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray with Icon"
        code={`import { BasicTimerIconGray } from '@cloc/atoms';

<BasicTimerIconGray />`}
      >
        <BasicTimerIconGray />
      </ComponentExample>

      <ComponentExample
        title="Gray Rounded with Icon"
        code={`import { BasicTimerIconGrayRounded } from '@cloc/atoms';

<BasicTimerIconGrayRounded />`}
      >
        <BasicTimerIconGrayRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray Full Rounded with Icon"
        code={`import { BasicTimerIconGrayFullRounded } from '@cloc/atoms';

<BasicTimerIconGrayFullRounded />`}
      >
        <BasicTimerIconGrayFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained with Icon"
        code={`import { BasicTimerIconContained } from '@cloc/atoms';

<BasicTimerIconContained />`}
      >
        <BasicTimerIconContained />
      </ComponentExample>

      <ComponentExample
        title="Contained Rounded with Icon"
        code={`import { BasicTimerIconContainedRounded } from '@cloc/atoms';

<BasicTimerIconContainedRounded />`}
      >
        <BasicTimerIconContainedRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained Full Rounded with Icon"
        code={`import { BasicTimerIconContainedFullRounded } from '@cloc/atoms';

<BasicTimerIconContainedFullRounded />`}
      >
        <BasicTimerIconContainedFullRounded />
      </ComponentExample>
    </div>
  );
}