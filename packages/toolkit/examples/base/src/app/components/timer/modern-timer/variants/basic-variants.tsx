'use client';

import { ModernCloc, ClocThemeToggle } from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function BasicVariants() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Modern Timer Variants</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-500">Toggle theme:</span>
          <ClocThemeToggle />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ComponentExample
            title="Default"
            code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="default"
  expanded={false}
  showProgress={false}
/>`}
          >
            <ModernCloc variant="default" size="default" expanded={false} showProgress={false} />
          </ComponentExample>

          <ComponentExample
            title="Border"
            code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="bordered"
  expanded={false}
  showProgress={false}
/>`}
          >
            <ModernCloc variant="bordered" expanded={false} showProgress={false} />
          </ComponentExample>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ComponentExample
            title="Small"
            code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="sm"
  expanded={false}
  showProgress={false}
/>`}
          >
            <ModernCloc variant="default" size="sm" expanded={false} showProgress={false} />
          </ComponentExample>

          <ComponentExample
            title="Resizable"
            code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  resizable
  expanded={false}
  showProgress={false}
/>`}
          >
            <ModernCloc variant="default" resizable expanded={false} showProgress={false} />
          </ComponentExample>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ComponentExample
            title="Large"
            code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="lg"
  expanded={false}
  showProgress={false}
/>`}
          >
            <ModernCloc variant="default" size="lg" expanded={false} showProgress={false} />
          </ComponentExample>
        </div>
      </div>
    </div>
  );
}