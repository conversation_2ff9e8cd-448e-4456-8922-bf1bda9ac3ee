'use client';

import {
    ClocBasicIcon,
	ClocBasicIconBorder,
	ClocBasicIconBorderFullRounded,
	ClocBasicIconBorderRounded,
	ClocBasicIconContained,
	ClocBasicIconContainedFullRounded,
	ClocBasicIconContainedRounded,
	ClocBasicIconGray,
	ClocBasicIconGrayFullRounded,
	ClocBasicIconGrayRounded,
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function IconVariants() {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            <ComponentExample
                title="Default with Icon"
                code={`import { ClocBasicIcon } from '@cloc/atoms';

<ClocBasicIcon />`}
            >
                <ClocBasicIcon />
            </ComponentExample>

            <ComponentExample
                title="Border with Icon"
                code={`import { ClocBasicIconBorder } from '@cloc/atoms';

<ClocBasicIconBorder />`}
            >
                <ClocBasicIconBorder />
            </ComponentExample>

            <ComponentExample
                title="Border Rounded with Icon"
                code={`import { ClocBasicIconBorderFullRounded } from '@cloc/atoms';

<ClocBasicIconBorderFullRounded />`}
            >
                <ClocBasicIconBorderFullRounded />
            </ComponentExample>

            <ComponentExample
                title="Gray with Icon"
                code={`import { ClocBasicIconGray } from '@cloc/atoms';

<ClocBasicIconGray />`}
            >
                <ClocBasicIconGray />
            </ComponentExample>

            <ComponentExample
                title="Gray Rounded with Icon"
                code={`import { ClocBasicIconGrayRounded } from '@cloc/atoms';

<ClocBasicIconGrayRounded />`}
            >
                <ClocBasicIconGrayRounded />
            </ComponentExample>

            <ComponentExample
                title="Gray Full Rounded with Icon"
                code={`import { ClocBasicIconGrayFullRounded } from '@cloc/atoms';

<ClocBasicIconGrayFullRounded />`}
            >
                <ClocBasicIconGrayFullRounded />
            </ComponentExample>

            <ComponentExample
                title="Contained with Icon"
                code={`import { ClocBasicIconContained } from '@cloc/atoms';

<ClocBasicIconContained />`}
            >
                <ClocBasicIconContained />
            </ComponentExample>

            <ComponentExample
                title="Contained Rounded with Icon"
                code={`import { ClocBasicIconContainedRounded } from '@cloc/atoms';

<ClocBasicIconContainedRounded />`}
            >
                <ClocBasicIconContainedRounded />
            </ComponentExample>

            <ComponentExample
                title="Contained Full Rounded with Icon"
                code={`import { ClocBasicIconContainedFullRounded } from '@cloc/atoms';

<ClocBasicIconContainedFullRounded />`}
            >
                <ClocBasicIconContainedFullRounded />
            </ComponentExample>

            <ComponentExample
                title="Border Full Rounded with Icon"
                code={`import { ClocBasicIconBorderRounded } from '@cloc/atoms';

<ClocBasicIconBorderRounded />`}
            >
                <ClocBasicIconBorderRounded />
            </ComponentExample>
        </div>
    );
}