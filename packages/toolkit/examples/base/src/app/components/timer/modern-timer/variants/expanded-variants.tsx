'use client';

import { ModernCloc } from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ExpandedVariants() {
  return (
    <div className="grid grid-cols-1 gap-8 p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Default Expanded"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  expanded={true}
  showProgress={false}
/>`}
        >
          <ModernCloc variant="default" expanded={true} showProgress={false} />
        </ComponentExample>

        <ComponentExample
          title="Border Expanded"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="bordered"
  expanded={true}
  showProgress={false}
/>`}
        >
          <ModernCloc variant="bordered" expanded={true} showProgress={false} />
        </ComponentExample>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Small Expanded"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="sm"
  expanded={true}
  showProgress={false}
/>`}
        >
          <ModernCloc variant="default" size="sm" expanded={true} showProgress={false} />
        </ComponentExample>

        <ComponentExample
          title="Resizable Expanded"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  resizable
  expanded={true}
  showProgress={false}
/>`}
        >
          <ModernCloc variant="default" resizable expanded={true} showProgress={false} />
        </ComponentExample>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Large Expanded"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="lg"
  expanded={true}
  showProgress={false}
/>`}
        >
          <ModernCloc variant="default" size="lg" expanded={true} showProgress={false} />
        </ComponentExample>
      </div>
    </div>
  );
}