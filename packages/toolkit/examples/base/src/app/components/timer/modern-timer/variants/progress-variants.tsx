'use client';

import { ModernCloc } from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ProgressVariants() {
  return (
    <div className="grid grid-cols-1 gap-8 p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Default with Progress"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="default"
  showProgress
  expanded={false}
/>`}
        >
          <ModernCloc variant="default" size="default" showProgress expanded={false} />
        </ComponentExample>

        <ComponentExample
          title="Border with Progress"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="bordered"
  showProgress
  expanded={false}
/>`}
        >
          <ModernCloc variant="bordered" showProgress expanded={false} />
        </ComponentExample>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Small with Progress"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="sm"
  showProgress
  expanded={false}
/>`}
        >
          <ModernCloc variant="default" size="sm" showProgress expanded={false} />
        </ComponentExample>

        <ComponentExample
          title="Resizable with Progress"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  resizable
  showProgress
  expanded={false}
/>`}
        >
          <ModernCloc variant="default" resizable showProgress expanded={false} />
        </ComponentExample>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ComponentExample
          title="Large with Progress"
          code={`import { ModernCloc } from '@cloc/atoms';

<ModernCloc 
  variant="default"
  size="lg"
  showProgress
  expanded={false}
/>`}
        >
          <ModernCloc variant="default" size="lg" showProgress expanded={false} />
        </ComponentExample>
      </div>
    </div>
  );
}