'use client';

import {
  BasicTimerIconProgress,
  BasicTimerIconBorderProgress,
  BasicTimerIconBorderRoundedProgress,
  BasicTimerIconBorderFullRoundedProgress,
  BasicTimerIconGrayProgress,
  BasicTimerIconGrayRoundedProgress,
  BasicTimerIconGrayFullRoundedProgress,
  BasicTimerIconContainedProgress,
  BasicTimerIconContainedRoundedProgress,
  BasicTimerIconContainedFullRoundedProgress
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ProgressVariants() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <ComponentExample
        title="Default with Progress"
        code={`import { BasicTimerIconProgress } from '@cloc/atoms';

<BasicTimerIconProgress />`}
      >
        <BasicTimerIconProgress />
      </ComponentExample>

      <ComponentExample
        title="Border with Progress"
        code={`import { BasicTimerIconBorderProgress } from '@cloc/atoms';

<BasicTimerIconBorderProgress />`}
      >
        <BasicTimerIconBorderProgress />
      </ComponentExample>

      <ComponentExample
        title="Border Rounded with Progress"
        code={`import { BasicTimerIconBorderRoundedProgress } from '@cloc/atoms';

<BasicTimerIconBorderRoundedProgress />`}
      >
        <BasicTimerIconBorderRoundedProgress />
      </ComponentExample>

      <ComponentExample
        title="Border Full Rounded with Progress"
        code={`import { BasicTimerIconBorderFullRoundedProgress } from '@cloc/atoms';

<BasicTimerIconBorderFullRoundedProgress />`}
      >
        <BasicTimerIconBorderFullRoundedProgress />
      </ComponentExample>

      <ComponentExample
        title="Gray with Progress"
        code={`import { BasicTimerIconGrayProgress } from '@cloc/atoms';

<BasicTimerIconGrayProgress />`}
      >
        <BasicTimerIconGrayProgress />
      </ComponentExample>

      <ComponentExample
        title="Gray Rounded with Progress"
        code={`import { BasicTimerIconGrayRoundedProgress } from '@cloc/atoms';

<BasicTimerIconGrayRoundedProgress />`}
      >
        <BasicTimerIconGrayRoundedProgress />
      </ComponentExample>

      <ComponentExample
        title="Gray Full Rounded with Progress"
        code={`import { BasicTimerIconGrayFullRoundedProgress } from '@cloc/atoms';

<BasicTimerIconGrayFullRoundedProgress />`}
      >
        <BasicTimerIconGrayFullRoundedProgress />
      </ComponentExample>

      <ComponentExample
        title="Contained with Progress"
        code={`import { BasicTimerIconContainedProgress } from '@cloc/atoms';

<BasicTimerIconContainedProgress />`}
      >
        <BasicTimerIconContainedProgress />
      </ComponentExample>

      <ComponentExample
        title="Contained Rounded with Progress"
        code={`import { BasicTimerIconContainedRoundedProgress } from '@cloc/atoms';

<BasicTimerIconContainedRoundedProgress />`}
      >
        <BasicTimerIconContainedRoundedProgress />
      </ComponentExample>

      <ComponentExample
        title="Contained Full Rounded with Progress"
        code={`import { BasicTimerIconContainedFullRoundedProgress } from '@cloc/atoms';

<BasicTimerIconContainedFullRoundedProgress />`}
      >
        <BasicTimerIconContainedFullRoundedProgress />
      </ComponentExample>
    </div>
  );
}