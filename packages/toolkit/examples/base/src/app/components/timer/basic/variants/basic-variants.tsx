'use client';

import {
  BasicTimer,
  BasicTimerBorder,
  BasicTimerBorderRounded,
  BasicTimerBorderFullRounded,
  BasicTimerGray,
  BasicTimerGrayRounded,
  BasicTimerGrayFullRounded,
  BasicTimerContained,
  BasicTimerContainedRounded,
  BasicTimerContainedFullRounded,
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function BasicVariants() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <ComponentExample
        title="Default"
        code={`import { BasicTimer } from '@cloc/atoms';

<BasicTimer background='destructive' rounded='small'/>`}
      >
        <BasicTimer background='destructive' color='primary' rounded='small'/>
      </ComponentExample>

      <ComponentExample
        title="Border"
        code={`import { BasicTimerBorder } from '@cloc/atoms';

<BasicTimerBorder />`}
      >
        <BasicTimerBorder />
      </ComponentExample>

      <ComponentExample
        title="Border Rounded"
        code={`import { BasicTimerBorderRounded } from '@cloc/atoms';

<BasicTimerBorderRounded />`}
      >
        <BasicTimerBorderRounded />
      </ComponentExample>

      <ComponentExample
        title="Border Full Rounded"
        code={`import { BasicTimerBorderFullRounded } from '@cloc/atoms';

<BasicTimerBorderFullRounded />`}
      >
        <BasicTimerBorderFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray"
        code={`import { BasicTimerGray } from '@cloc/atoms';

<BasicTimerGray />`}
      >
        <BasicTimerGray />
      </ComponentExample>

      <ComponentExample
        title="Gray Rounded"
        code={`import { BasicTimerGrayRounded } from '@cloc/atoms';

<BasicTimerGrayRounded />`}
      >
        <BasicTimerGrayRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray Full Rounded"
        code={`import { BasicTimerGrayFullRounded } from '@cloc/atoms';

<BasicTimerGrayFullRounded />`}
      >
        <BasicTimerGrayFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained"
        code={`import { BasicTimerContained } from '@cloc/atoms';

<BasicTimerContained />`}
      >
        <BasicTimerContained />
      </ComponentExample>

      <ComponentExample
        title="Contained Rounded"
        code={`import { BasicTimerContainedRounded } from '@cloc/atoms';

<BasicTimerContainedRounded />`}
      >
        <BasicTimerContainedRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained Full Rounded"
        code={`import { BasicTimerContainedFullRounded } from '@cloc/atoms';

<BasicTimerContainedFullRounded />`}
      >
        <BasicTimerContainedFullRounded />
      </ComponentExample>
    </div>
  );
}