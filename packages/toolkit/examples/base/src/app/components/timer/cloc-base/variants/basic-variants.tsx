'use client';

import {
  C<PERSON><PERSON>asi<PERSON>,
  Cloc<PERSON>asi<PERSON>Border,
  ClocBasicBorderRounded,
  ClocBasicBorderFullRounded,
  <PERSON>locBasic<PERSON>ray,
  ClocBasicGrayRounded,
  <PERSON>locBasicGrayFullRounded,
  Cloc<PERSON>asicContained,
  ClocBasicContainedRounded,
  ClocBasicContainedFullRounded,
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function BasicVariants() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <ComponentExample
        title="Default"
        code={`import { ClocBasic } from '@cloc/atoms';

<ClocBasic progress={false} />`}
      >
        <ClocBasic progress={false} key="default" />
      </ComponentExample>

      <ComponentExample
        title="Border"
        code={`import { ClocBasicBorder } from '@cloc/atoms';

<ClocBasicBorder />`}
      >
        <ClocBasicBorder />
      </ComponentExample>

      <ComponentExample
        title="Border Rounded"
        code={`import { ClocBasicBorderRounded } from '@cloc/atoms';

<ClocBasicBorderRounded />`}
      >
        <ClocBasicBorderRounded />
      </ComponentExample>

      <ComponentExample
        title="Border Full Rounded"
        code={`import { ClocBasicBorderFullRounded } from '@cloc/atoms';

<ClocBasicBorderFullRounded />`}
      >
        <ClocBasicBorderFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray"
        code={`import { ClocBasicGray } from '@cloc/atoms';

<ClocBasicGray />`}
      >
        <ClocBasicGray />
      </ComponentExample>

      <ComponentExample
        title="Gray Rounded"
        code={`import { ClocBasicGrayRounded } from '@cloc/atoms';

<ClocBasicGrayRounded />`}
      >
        <ClocBasicGrayRounded />
      </ComponentExample>

      <ComponentExample
        title="Gray Full Rounded"
        code={`import { ClocBasicGrayFullRounded } from '@cloc/atoms';

<ClocBasicGrayFullRounded />`}
      >
        <ClocBasicGrayFullRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained"
        code={`import { ClocBasicContained } from '@cloc/atoms';

<ClocBasicContained />`}
      >
        <ClocBasicContained />
      </ComponentExample>

      <ComponentExample
        title="Contained Rounded"
        code={`import { ClocBasicContainedRounded } from '@cloc/atoms';

<ClocBasicContainedRounded />`}
      >
        <ClocBasicContainedRounded />
      </ComponentExample>

      <ComponentExample
        title="Contained Full Rounded"
        code={`import { ClocBasicContainedFullRounded } from '@cloc/atoms';

<ClocBasicContainedFullRounded />`}
      >
        <ClocBasicContainedFullRounded />
      </ComponentExample>
    </div>
  );
}