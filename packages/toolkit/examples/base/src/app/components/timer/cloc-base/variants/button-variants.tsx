'use client';

import {
	ClocBasicIconBorderFullRoundedProgressButton,
	ClocBasicIconBorderProgressButton,
	ClocBasicIconBorderRoundedProgressButton,
	ClocBasicIconContainedFullRoundedProgressButton,
	ClocBasicIconContainedProgressButton,
	ClocBasicIconContainedRoundedProgressButton,
	ClocBasicIconGrayFullRoundedProgressButton,
	ClocBasicIconGrayProgressButton,
	ClocBasicIconGrayRoundedProgressButton,
	ClocBasicIconProgressButton,
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ButtonVariants() {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
			<ComponentExample
				title="Default with Button"
				code={`import { ClocBasicIconProgressButton } from '@cloc/atoms';

<ClocBasicIconProgressButton />`}
			>
				<ClocBasicIconProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Border with Button"
				code={`import { ClocBasicIconBorderProgressButton } from '@cloc/atoms';

<ClocBasicIconBorderProgressButton />`}
			>
				<ClocBasicIconBorderProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Border Rounded with Button"
				code={`import { ClocBasicIconBorderRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconBorderRoundedProgressButton />`}
			>
				<ClocBasicIconBorderRoundedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Border Full Rounded with Button"
				code={`import { ClocBasicIconBorderFullRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconBorderFullRoundedProgressButton />`}
			>
				<ClocBasicIconBorderFullRoundedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Gray with Button"
				code={`import { ClocBasicIconGrayProgressButton } from '@cloc/atoms';

<ClocBasicIconGrayProgressButton />`}
			>
				<ClocBasicIconGrayProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Gray Rounded with Button"
				code={`import { ClocBasicIconGrayRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconGrayRoundedProgressButton />`}
			>
				<ClocBasicIconGrayRoundedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Gray Full Rounded with Button"
				code={`import { ClocBasicIconGrayFullRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconGrayFullRoundedProgressButton />`}
			>
				<ClocBasicIconGrayFullRoundedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Contained with Button"
				code={`import { ClocBasicIconContainedProgressButton } from '@cloc/atoms';

<ClocBasicIconContainedProgressButton />`}
			>
				<ClocBasicIconContainedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Contained Rounded with Button"
				code={`import { ClocBasicIconContainedRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconContainedRoundedProgressButton />`}
			>
				<ClocBasicIconContainedRoundedProgressButton />
			</ComponentExample>

			<ComponentExample
				title="Contained Full Rounded with Button"
				code={`import { ClocBasicIconContainedFullRoundedProgressButton } from '@cloc/atoms';

<ClocBasicIconContainedFullRoundedProgressButton />`}
			>
				<ClocBasicIconContainedFullRoundedProgressButton />
			</ComponentExample>
		</div>
	);
}