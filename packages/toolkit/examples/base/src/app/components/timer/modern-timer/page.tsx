'use client';

import { ClocThemeToggle } from '@cloc/atoms';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@cloc/ui';
import { BasicVariants } from './variants/basic-variants';
import { ProgressVariants } from './variants/progress-variants';
import { ExpandedVariants } from './variants/expanded-variants';
import { PageTitle } from '@/components/page-title';

export default function ModernTimerPage() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Modern Timer Components"
				description="Modern timer components with enhanced visual design and functionality."
			/>

			<div className="flex flex-col justify-start mb-4 max-w-sm">
				<h3 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-3">Select component theme:</h3>
				<ClocThemeToggle />
			</div>

			<Tabs defaultValue="basic" className="w-full">
				<TabsList>
					<TabsTrigger value="basic">Basic Variants</TabsTrigger>
					<TabsTrigger value="progress">With Progress</TabsTrigger>
					<TabsTrigger value="expanded">Expanded</TabsTrigger>
				</TabsList>

				<TabsContent value="basic">
					<BasicVariants />
				</TabsContent>

				<TabsContent value="progress">
					<ProgressVariants />
				</TabsContent>

				<TabsContent value="expanded">
					<ExpandedVariants />
				</TabsContent>
			</Tabs>
		</div>
	);
}
