'use client';

import {
	ClocBasicIconBorderFullRoundedProgress,
	ClocBasicIconBorderProgress,
	ClocBasicIconBorderRoundedProgress,
	ClocBasicIconContainedFullRoundedProgress,
	ClocBasicIconContainedProgress,
	ClocBasicIconContainedRoundedProgress,
	ClocBasicIconGrayFullRoundedProgress,
	ClocBasicIconGrayProgress,
	ClocBasicIconGrayRoundedProgress,
	ClocBasicIconProgress,
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ProgressVariants() {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
			<ComponentExample
				title="Default with Progress"
				code={`import { ClocBasicIconProgress } from '@cloc/atoms';

<ClocBasicIconProgress />`}
			>
				<ClocBasicIconProgress />
			</ComponentExample>

			<ComponentExample
				title="Border with Progress"
				code={`import { ClocBasicIconBorderProgress } from '@cloc/atoms';

<ClocBasicIconBorderProgress />`}
			>
				<ClocBasicIconBorderProgress />
			</ComponentExample>

			<ComponentExample
				title="Border Rounded with Progress"
				code={`import { ClocBasicIconBorderRoundedProgress } from '@cloc/atoms';

<ClocBasicIconBorderRoundedProgress />`}
			>
				<ClocBasicIconBorderRoundedProgress />
			</ComponentExample>

			<ComponentExample
				title="Border Full Rounded with Progress"
				code={`import { ClocBasicIconBorderFullRoundedProgress } from '@cloc/atoms';

<ClocBasicIconBorderFullRoundedProgress />`}
			>
				<ClocBasicIconBorderFullRoundedProgress />
			</ComponentExample>

			<ComponentExample
				title="Gray with Progress"
				code={`import { ClocBasicIconGrayProgress } from '@cloc/atoms';

<ClocBasicIconGrayProgress />`}
			>
				<ClocBasicIconGrayProgress />
			</ComponentExample>

			<ComponentExample
				title="Gray Rounded with Progress"
				code={`import { ClocBasicIconGrayRoundedProgress } from '@cloc/atoms';

<ClocBasicIconGrayRoundedProgress />`}
			>
				<ClocBasicIconGrayRoundedProgress />
			</ComponentExample>

			<ComponentExample
				title="Gray Full Rounded with Progress"
				code={`import { ClocBasicIconGrayFullRoundedProgress } from '@cloc/atoms';

<ClocBasicIconGrayFullRoundedProgress />`}
			>
				<ClocBasicIconGrayFullRoundedProgress />
			</ComponentExample>

			<ComponentExample
				title="Contained with Progress"
				code={`import { ClocBasicIconContainedProgress } from '@cloc/atoms';

<ClocBasicIconContainedProgress />`}
			>
				<ClocBasicIconContainedProgress />
			</ComponentExample>

			<ComponentExample
				title="Contained Rounded with Progress"
				code={`import { ClocBasicIconContainedRoundedProgress } from '@cloc/atoms';

<ClocBasicIconContainedRoundedProgress />`}
			>
				<ClocBasicIconContainedRoundedProgress />
			</ComponentExample>

			<ComponentExample
				title="Contained Full Rounded with Progress"
				code={`import { ClocBasicIconContainedFullRoundedProgress } from '@cloc/atoms';

<ClocBasicIconContainedFullRoundedProgress />`}
			>
				<ClocBasicIconContainedFullRoundedProgress />
			</ComponentExample>
		</div>
	);
}