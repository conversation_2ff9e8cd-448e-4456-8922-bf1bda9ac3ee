'use client';

import {
  BasicTimerIconProgressButton,
  BasicTimerIconBorderProgressButton,
  BasicTimerIconBorderRoundedProgressButton,
  BasicTimerIconBorderFullRoundedProgressButton,
  BasicTimerIconGrayProgressButton,
  BasicTimerIconGrayRoundedProgressButton,
  BasicTimerIconGrayFullRoundedProgressButton,
  BasicTimerIconContainedProgressButton,
  BasicTimerIconContainedRoundedProgressButton,
  BasicTimerIconContainedFullRoundedProgressButton
} from '@cloc/atoms';
import { ComponentExample } from '@/components/component-example';

export function ButtonVariants() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      <ComponentExample
        title="Default with Button"
        code={`import { BasicTimerIconProgressButton } from '@cloc/atoms';

<BasicTimerIconProgressButton />`}
      >
        <BasicTimerIconProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Border with Button"
        code={`import { BasicTimerIconBorderProgressButton } from '@cloc/atoms';

<BasicTimerIconBorderProgressButton />`}
      >
        <BasicTimerIconBorderProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Border Rounded with Button"
        code={`import { BasicTimerIconBorderRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconBorderRoundedProgressButton />`}
      >
        <BasicTimerIconBorderRoundedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Border Full Rounded with Button"
        code={`import { BasicTimerIconBorderFullRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconBorderFullRoundedProgressButton />`}
      >
        <BasicTimerIconBorderFullRoundedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Gray with Button"
        code={`import { BasicTimerIconGrayProgressButton } from '@cloc/atoms';

<BasicTimerIconGrayProgressButton />`}
      >
        <BasicTimerIconGrayProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Gray Rounded with Button"
        code={`import { BasicTimerIconGrayRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconGrayRoundedProgressButton />`}
      >
        <BasicTimerIconGrayRoundedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Gray Full Rounded with Button"
        code={`import { BasicTimerIconGrayFullRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconGrayFullRoundedProgressButton />`}
      >
        <BasicTimerIconGrayFullRoundedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Contained with Button"
        code={`import { BasicTimerIconContainedProgressButton } from '@cloc/atoms';

<BasicTimerIconContainedProgressButton />`}
      >
        <BasicTimerIconContainedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Contained Rounded with Button"
        code={`import { BasicTimerIconContainedRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconContainedRoundedProgressButton />`}
      >
        <BasicTimerIconContainedRoundedProgressButton />
      </ComponentExample>

      <ComponentExample
        title="Contained Full Rounded with Button"
        code={`import { BasicTimerIconContainedFullRoundedProgressButton } from '@cloc/atoms';

<BasicTimerIconContainedFullRoundedProgressButton />`}
      >
        <BasicTimerIconContainedFullRoundedProgressButton />
      </ComponentExample>
    </div>
  );
}