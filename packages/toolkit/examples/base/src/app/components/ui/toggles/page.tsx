'use client';

import { ClocThemeToggle, ClocFontToggle } from '@cloc/atoms';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

export default function ToggleVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Toggle Components"
				description="Collection of toggle components for theme and font customization in Cloc applications."
			/>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Theme Toggle"
						code={`import { ClocThemeToggle } from '@cloc/atoms';

<ClocThemeToggle />`}
					>
						<ClocThemeToggle />
					</ComponentExample>

					<ComponentExample
						title="Font Toggle"
						code={`import { ClocFontToggle } from '@cloc/atoms';

<ClocFontToggle />`}
					>
						<ClocFontToggle />
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
