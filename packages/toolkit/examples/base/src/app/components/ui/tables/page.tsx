'use client';

import { ClocTable } from '@cloc/atoms';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

const sampleData = [
	{
		id: 1,
		date: '2024-03-20',
		project: 'Project A',
		duration: '2h 30m',
		status: 'Completed'
	},
	{
		id: 2,
		date: '2024-03-20',
		project: 'Project B',
		duration: '1h 45m',
		status: 'In Progress'
	},
	{
		id: 3,
		date: '2024-03-19',
		project: 'Project C',
		duration: '4h 15m',
		status: 'Completed'
	},
	{
		id: 4,
		date: '2024-03-19',
		project: 'Project D',
		duration: '3h 00m',
		status: 'Paused'
	},
	{
		id: 5,
		date: '2024-03-18',
		project: 'Project E',
		duration: '5h 30m',
		status: 'Completed'
	}
];

export default function TableVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Table Components"
				description="Collection of table components for displaying data in Cloc applications."
			/>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 gap-4">
					<ComponentExample
						title="Basic Table"
						code={`import { ClocTable } from '@cloc/atoms';

const sampleData = [
  {
    id: 1,
    date: '2024-03-20',
    project: 'Project A',
    duration: '2h 30m',
    status: 'Completed'
  },
  // ... more data
];

<ClocTable data={sampleData} />`}
					>
						<ClocTable data={sampleData} />
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
