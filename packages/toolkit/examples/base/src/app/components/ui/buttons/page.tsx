'use client';

import { C<PERSON><PERSON>utton, ClocThemeToggle } from '@cloc/atoms';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

export default function ButtonVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Button Components"
				description="Collection of button variants and styles for Cloc applications."
			/>

			<div className="flex flex-col justify-start mb-4 max-w-sm">
				<h3 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-3">Select component theme:</h3>
				<ClocThemeToggle />
			</div>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Default Button"
						code={`import { ClocButton } from '@cloc/atoms';

<ClocButton>Default Button</ClocButton>`}
					>
						<ClocButton>Default Button</ClocButton>
					</ComponentExample>

					<ComponentExample
						title="Primary Button"
						code={`import { ClocButton } from '@cloc/atoms';

<ClocButton variant="default">Primary Button</ClocButton>`}
					>
						<ClocButton variant="default">Primary Button</ClocButton>
					</ComponentExample>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Secondary Button"
						code={`import { ClocButton } from '@cloc/atoms';

<ClocButton variant="secondary">Secondary Button</ClocButton>`}
					>
						<ClocButton variant="secondary">Secondary Button</ClocButton>
					</ComponentExample>

					<ComponentExample
						title="Outline Button"
						code={`import { ClocButton } from '@cloc/atoms';

<ClocButton variant="outline">Outline Button</ClocButton>`}
					>
						<ClocButton variant="outline">Outline Button</ClocButton>
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
