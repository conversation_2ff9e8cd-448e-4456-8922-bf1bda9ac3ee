'use client';

import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	ClocLoginForm,
	ClocRegistrationForm,
	ClocLoginDialog,
	ClocRegistrationDialog
} from '@cloc/atoms';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

export default function FormVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Form Components"
				description="Collection of form components and variants for Cloc applications."
			/>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Timer Form"
						code={`import { ClocTimerForm } from '@cloc/atoms';

<ClocTimerForm />`}
					>
						<ClocTimerForm />
					</ComponentExample>
					<ComponentExample
						title="Login Form"
						code={`import { ClocLoginForm } from '@cloc/atoms';

<ClocLoginForm />`}
					>
						<ClocLoginForm />
					</ComponentExample>
					<ComponentExample
						title="Sign Up Form"
						code={`import { ClocRegistrationForm } from '@cloc/atoms';

<ClocRegistrationForm />`}
					>
						<ClocRegistrationForm />
					</ComponentExample>
					<ComponentExample
						title="Login Dialog"
						code={`import { ClocLoginDialog } from '@cloc/atoms';

<ClocLoginDialog />`}
					>
						<ClocLoginDialog />
					</ComponentExample>
					<ComponentExample
						title="Sign Up Dialog"
						code={`import { ClocRegistrationDialog } from '@cloc/atoms';

<ClocRegistrationDialog />`}
					>
						<ClocRegistrationDialog />
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
