'use client';

import { MultiSelect } from '@cloc/ui';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

const sampleItems = [
	{ label: '<PERSON>', progress: 30, color: '#34d399' },
	{ label: '<PERSON>', progress: 25, color: '#eab308' },
	{ label: '<PERSON><PERSON> Buli<PERSON>ne', progress: 75, color: '#eab308' },
	{ label: 'Innocent Akim', progress: 100, color: '#10b981' }
];

export default function MultiSelectVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="MultiSelect Components"
				description="Collection of multiselect components for user selection in Cloc applications."
			/>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Basic MultiSelect"
						code={`import { MultiSelect } from '@cloc/ui';

const sampleItems = [
  { label: '<PERSON>', progress: 30, color: '#34d399' },
  { label: '<PERSON>', progress: 25, color: '#eab308' },
  { label: '<PERSON>ck Bulienine', progress: 75, color: '#eab308' },
  { label: 'Innocent Akim', progress: 100, color: '#10b981' }
];

<MultiSelect
  items={sampleItems}
  itemToString={(values) => (values ? values.label : '')}
  itemId={(item) => item.label}
  onValueChange={(selectedItems) => console.log('selectedItems:', selectedItems)}
  multiSelect={true}
  triggerClassName="dark:border-gray-700"
/>`}
					>
						<MultiSelect
							items={sampleItems}
							itemToString={(values) => (values ? values.label : '')}
							itemId={(item) => item.label}
							onValueChange={(selectedItems) => console.log('selectedItems:', selectedItems)}
							multiSelect={true}
							triggerClassName="dark:border-gray-700"
						/>
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
