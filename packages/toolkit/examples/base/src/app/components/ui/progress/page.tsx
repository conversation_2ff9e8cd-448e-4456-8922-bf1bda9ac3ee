'use client';

import { ClocProgressCircle } from '@cloc/atoms';
import { PageTitle } from '@/components/page-title';
import { ComponentExample } from '@/components/component-example';

export default function ProgressVariants() {
	return (
		<div className="space-y-8">
			<PageTitle
				title="Progress Components"
				description="Collection of progress indicators and circle progress components for Cloc applications."
			/>

			<div className="grid grid-cols-1 gap-8 p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<ComponentExample
						title="Circle Progress"
						code={`import { ClocProgressCircle } from '@cloc/atoms';

<ClocProgressCircle />`}
					>
						<ClocProgressCircle />
					</ComponentExample>
				</div>
			</div>
		</div>
	);
}
