'use client';

import React from 'react';

import {
	BasicTimerContainedFullRounded,
	BasicTimerContainedRounded,
	BasicTimerGray,
	BasicTimerGrayFullRounded,
	BasicTimerGrayRounded,
	BasicTimerIcon,
	BasicTimerIconBorder,
	BasicTimerIconBorderFullRounded,
	BasicTimerIconBorderFullRoundedProgress,
	BasicTimerIconBorderFullRoundedProgressButton,
	BasicTimerIconBorderProgress,
	BasicTimerIconBorderProgressButton,
	BasicTimerIconBorderRounded,
	BasicTimerIconBorderRoundedProgress,
	BasicTimerIconBorderRoundedProgressButton,
	BasicTimerIconContained,
	BasicTimerIconContainedFullRounded,
	BasicTimerIconContainedFullRoundedProgress,
	BasicTimerIconContainedFullRoundedProgressButton,
	BasicTimerIconContainedProgress,
	BasicTimerIconContainedProgressButton,
	BasicTimerIconContainedRounded,
	BasicTimerIconContainedRoundedProgress,
	BasicTimerIconContainedRoundedProgressButton,
	BasicTimerIconGray,
	BasicTimerIconGrayFullRounded,
	BasicTimerIconGrayFullRoundedProgress,
	BasicTimerIconGrayFullRoundedProgressButton,
	BasicTimerIconGrayProgress,
	BasicTimerIconGrayProgressButton,
	BasicTimerIconGrayRounded,
	BasicTimerIconGrayRoundedProgress,
	BasicTimerIconGrayRoundedProgressButton,
	BasicTimerIconProgress,
	BasicTimerIconProgressButton,
	ClocBasic,
	ClocBasicBorder,
	ClocBasicBorderFullRounded,
	ClocBasicBorderRounded,
	ClocBasicContained,
	ClocBasicContainedFullRounded,
	ClocBasicContainedRounded,
	ClocBasicGray,
	ClocBasicGrayFullRounded,
	ClocBasicGrayRounded,
	ClocBasicIcon,
	ClocBasicIconBorder,
	ClocBasicIconBorderFullRounded,
	ClocBasicIconBorderFullRoundedProgress,
	ClocBasicIconBorderFullRoundedProgressButton,
	ClocBasicIconBorderProgress,
	ClocBasicIconBorderProgressButton,
	ClocBasicIconBorderRounded,
	ClocBasicIconBorderRoundedProgress,
	ClocBasicIconBorderRoundedProgressButton,
	ClocBasicIconContained,
	ClocBasicIconContainedFullRounded,
	ClocBasicIconContainedFullRoundedProgress,
	ClocBasicIconContainedFullRoundedProgressButton,
	ClocBasicIconContainedProgress,
	ClocBasicIconContainedProgressButton,
	ClocBasicIconContainedRounded,
	ClocBasicIconContainedRoundedProgress,
	ClocBasicIconContainedRoundedProgressButton,
	ClocBasicIconGray,
	ClocBasicIconGrayFullRounded,
	ClocBasicIconGrayFullRoundedProgress,
	ClocBasicIconGrayFullRoundedProgressButton,
	ClocBasicIconGrayProgress,
	ClocBasicIconGrayProgressButton,
	ClocBasicIconGrayRounded,
	ClocBasicIconGrayRoundedProgress,
	ClocBasicIconGrayRoundedProgressButton,
	ClocBasicIconProgress,
	ClocBasicIconProgressButton,
	ModernCloc,
	BasicClocReport,
	fakedataTable,
	ClocProgressCircle,
	MemberCloc,
	ClocTable,
	BasicTimer,
	BasicTimerBorder,
	BasicTimerBorderRounded,
	BasicTimerBorderFullRounded,
	BasicTimerContained,
	ClocFontToggle
} from '@cloc/atoms';
import { ToggleThemeContainer } from '@cloc/atoms';
import { ClocLogo, MultiSelect, Toaster } from '@cloc/ui';

export default function Page(): React.JSX.Element {
	return (
		<main className="dark:bg-gray-900">
			<div className="flex items-start gap-8 bg-gray-50 dark:bg-gray-900 flex-wrap p-24 pt-32">
				<div className="flex flex-col items-start gap-4">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">Basics</h1>
					<BasicTimer />
					<BasicTimerBorder />
					<BasicTimerBorderRounded />
					<BasicTimerBorderFullRounded />
					<BasicTimerGray />
					<BasicTimerGrayRounded />
					<BasicTimerGrayFullRounded />
					<BasicTimerContained />
					<BasicTimerContainedRounded />
					<BasicTimerContainedFullRounded />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Icons</h1>

					<BasicTimerIcon />
					<BasicTimerIconBorder />
					<BasicTimerIconBorderRounded />
					<BasicTimerIconBorderFullRounded />
					<BasicTimerIconGray />
					<BasicTimerIconGrayRounded />
					<BasicTimerIconGrayFullRounded />
					<BasicTimerIconContained />
					<BasicTimerIconContainedRounded />
					<BasicTimerIconContainedFullRounded />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Progress</h1>
					<BasicTimerIconProgress />
					<BasicTimerIconBorderProgress />
					<BasicTimerIconBorderRoundedProgress />
					<BasicTimerIconBorderFullRoundedProgress />
					<BasicTimerIconGrayProgress />
					<BasicTimerIconGrayRoundedProgress />
					<BasicTimerIconGrayFullRoundedProgress />
					<BasicTimerIconContainedProgress />
					<BasicTimerIconContainedRoundedProgress />
					<BasicTimerIconContainedFullRoundedProgress />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Button</h1>
					<BasicTimerIconProgressButton />
					<BasicTimerIconBorderProgressButton />
					<BasicTimerIconBorderRoundedProgressButton />
					<BasicTimerIconBorderFullRoundedProgressButton />
					<BasicTimerIconGrayProgressButton />
					<BasicTimerIconGrayRoundedProgressButton />
					<BasicTimerIconGrayFullRoundedProgressButton />
					<BasicTimerIconContainedProgressButton />
					<BasicTimerIconContainedRoundedProgressButton />
					<BasicTimerIconContainedFullRoundedProgressButton />
				</div>
			</div>
			<hr />
			<div className="flex items-start gap-8 bg-gray-50 dark:bg-gray-900 flex-wrap p-24">
				<div className="flex flex-col items-start gap-4">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">Basics</h1>
					<ClocBasic />
					<ClocBasicBorder />
					<ClocBasicBorderRounded />
					<ClocBasicBorderFullRounded />
					<ClocBasicGray />
					<ClocBasicGrayRounded />
					<ClocBasicGrayFullRounded />
					<ClocBasicContained />
					<ClocBasicContainedRounded />
					<ClocBasicContainedFullRounded />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Icons</h1>
					<ClocBasicIcon />
					<ClocBasicIconBorder />
					<ClocBasicIconBorderRounded />
					<ClocBasicIconBorderFullRounded />
					<ClocBasicIconGray />
					<ClocBasicIconGrayRounded />
					<ClocBasicIconGrayFullRounded />
					<ClocBasicIconContained />
					<ClocBasicIconContainedRounded />
					<ClocBasicIconContainedFullRounded />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Progress</h1>
					<ClocBasicIconProgress />
					<ClocBasicIconBorderProgress />
					<ClocBasicIconBorderRoundedProgress />
					<ClocBasicIconBorderFullRoundedProgress />
					<ClocBasicIconGrayProgress />
					<ClocBasicIconGrayRoundedProgress />
					<ClocBasicIconGrayFullRoundedProgress />
					<ClocBasicIconContainedProgress />
					<ClocBasicIconContainedRoundedProgress />
					<ClocBasicIconContainedFullRoundedProgress />
				</div>

				<div className="flex flex-col items-start gap-4 ">
					<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">With Button</h1>
					<ClocBasicIconProgressButton />
					<ClocBasicIconBorderProgressButton />
					<ClocBasicIconBorderRoundedProgressButton />
					<ClocBasicIconBorderFullRoundedProgressButton />
					<ClocBasicIconGrayProgressButton />
					<ClocBasicIconGrayRoundedProgressButton />
					<ClocBasicIconGrayFullRoundedProgressButton />
					<ClocBasicIconContainedProgressButton />
					<ClocBasicIconContainedRoundedProgressButton />
					<ClocBasicIconContainedFullRoundedProgressButton />
					<ClocFontToggle />
				</div>
			</div>
			<hr />

			<div className="flex flex-col  items-start gap-8 bg-gray-50 dark:bg-gray-900  p-10">
				<h1 className="text-md font-semibold text-gray-700 dark:text-gray-100">Modern Timer</h1>

				<div className="flex items-start gap-4 flex-wrap ">
					<ModernCloc expanded={false} showProgress variant={'default'} size={'sm'} />
					<ModernCloc expanded={false} showProgress={false} variant={'default'} size={'sm'} />
					<ModernCloc expanded={false} showProgress variant={'bordered'} size={'sm'} />
					<ModernCloc expanded={false} showProgress variant={'default'} />
					<ModernCloc showProgress variant={'bordered'} size={'default'} expanded={false} />
					<ModernCloc showProgress variant={'default'} resizable expanded={false} />
				</div>
			</div>
			{/* <hr /> */}
			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8 bg-gray-50  min-h-screen p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100">Modern Timer</h1>
				<div className="flex items-start flex-wrap gap-4 ">
					<ModernCloc expanded={false} showProgress variant={'default'} />
					<ModernCloc showProgress variant={'default'} size={'default'} expanded />
					<ModernCloc showProgress variant={'default'} resizable expanded />
					<ModernCloc expanded={false} showProgress variant={'bordered'} />
					<ModernCloc showProgress variant={'bordered'} size={'default'} expanded />
					<ModernCloc showProgress variant={'bordered'} size={'sm'} resizable expanded />
					<ModernCloc showProgress variant={'bordered'} size={'sm'} expanded />
					<ModernCloc showProgress variant={'bordered'} size={'sm'} expanded />
					<ModernCloc showProgress variant={'bordered'} size={'lg'} expanded />
				</div>
			</div>
			<hr />
			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8 bg-gray-50  min-h-screen p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100">Cloc Report</h1>

				<div className="flex items-start flex-wrap gap-4 ">
					<BasicClocReport type="bar-vertical" />
					<BasicClocReport type="bar-vertical" variant={'bordered'} />
					<BasicClocReport type="bar" />
					<BasicClocReport type="line" />
					<BasicClocReport type="area" />
					<BasicClocReport type="tooltip" />
					<BasicClocReport type="radial" />
					<BasicClocReport type="radar" />
					<BasicClocReport type="pie" />
				</div>
			</div>

			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8 bg-gray-50  min-h-screen p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100">Members Activities</h1>
				<ToggleThemeContainer />
				<div className="flex items-stretch gap-4 w-full">
					<MemberCloc showProgress={true} showTime={true} />
					<MemberCloc showProgress={true} showTime={true} />
					<MemberCloc showProgress={false} showTime={true} />

					<MemberCloc showProgress={true} showTime={false} />
				</div>
			</div>
			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8 bg-gray-50  p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100">Progress Circle</h1>
				<ToggleThemeContainer />
				<div className="flex justify-between gap-4  w-full h-80  border-dashed border-secondaryColor rounded-lg border-4">
					<ClocProgressCircle />
					<ClocProgressCircle />
					<ClocProgressCircle />
					<ClocProgressCircle />
				</div>
			</div>
			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8 bg-gray-50  p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100">MultiSelect</h1>
				<ToggleThemeContainer />
				<div className="flex items-stretch gap-4 w-full">
					<MultiSelect
						items={[
							{ label: 'Kevin Peterson', progress: 30, color: '#34d399' },
							{ label: 'Josh Kenan', progress: 25, color: '#eab308' },
							{ label: 'Arick Bulienine', progress: 75, color: '#eab308' },
							{ label: 'Innocent Akim', progress: 100, color: '#10b981' }
						]}
						itemToString={(values) => (values ? values.label : '')}
						itemId={(item) => item.label}
						onValueChange={(selectedItems) => console.log(selectedItems)}
						multiSelect={true} // Enable multi-select
						triggerClassName="dark:border-gray-700"
					/>
					<MultiSelect
						items={[
							{ label: 'Kevin Peterson', progress: 30, color: '#34d399' },
							{ label: 'Josh Kenan', progress: 25, color: '#eab308' },
							{ label: 'Arick Bulienine', progress: 75, color: '#eab308' },
							{ label: 'Innocent Akim', progress: 100, color: '#10b981' }
						]}
						itemToString={(values) => (values ? values.label : '')}
						itemId={(item) => item.label}
						onValueChange={(selectedItems) => console.log(selectedItems)}
						multiSelect={true} // Enable multi-select
						triggerClassName="dark:border-gray-700"
					/>
					<MultiSelect
						items={[
							{ label: 'Kevin Peterson', progress: 30, color: '#34d399' },
							{ label: 'Josh Kenan', progress: 25, color: '#eab308' },
							{ label: 'Arick Bulienine', progress: 75, color: '#eab308' },
							{ label: 'Innocent Akim', progress: 100, color: '#10b981' }
						]}
						itemToString={(values) => (values ? values.label : '')}
						itemId={(item) => item.label}
						onValueChange={(selectedItems) => console.log('selectedItems :', selectedItems)}
						multiSelect={true} // Enable multi-select
						triggerClassName="dark:border-gray-700"
					/>
				</div>
			</div>
			<div className=" flex flex-col dark:bg-gray-900 items-start gap-8  p-10">
				<h1 className=" text-md font-semibold text-gray-700 dark:text-gray-100 rounded ">Table</h1>
				<ToggleThemeContainer />
				<div className="flex items-stretch gap-4 w-full ">
					<ClocTable
						data={fakedataTable}
						caption="A list of your recent invoices."
						footerData={{ label: 'Total', value: '$2,500.00' }}
						renderHeader={(column) => column.toUpperCase()}
						renderCell={(row, column) => (
							<span
								className={`${row[column] !== 'Paid' ? 'dark:text-white' : ''}`}
								style={{
									color: column === 'paymentStatus' && row[column] === 'Paid' ? 'green' : 'black'
								}}
							>
								{row[column]}
							</span>
						)}
						tableClassName="border-collapse w-full dark:text-white"
						headerClassName="bg-gray-200 dark:text-white"
						rowClassName="hover:bg-gray-100 dark:text-white"
						cellClassName="p-4 border dark:text-white"
						footerClassName="font-bold"
					/>
				</div>
			</div>
			<Toaster />
		</main>
	);
}
