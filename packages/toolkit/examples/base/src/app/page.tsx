'use client';

import { But<PERSON> } from '@cloc/ui';
import Link from 'next/link';
import { ArrowRightIcon } from '@radix-ui/react-icons';
import { ClocThemeToggle, ModernCloc } from '@cloc/atoms';

export default function Page() {
	return (
		<div className="flex flex-col min-h-screen">
			{/* Hero Section */}
			<div className="flex flex-col items-center justify-center px-4 py-32 text-center space-y-8 bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
				{/* <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400 bg-slate-200/50 dark:bg-slate-800/50 py-1 px-3 rounded-full">
          <span>Now with drag and drop builder</span>
          <Link href="/grapesjs" className="underline">Try it out →</Link>
        </div> */}

				<h1 className="text-6xl font-bold tracking-tight text-slate-900 dark:text-slate-100">Cloc SDK</h1>
				<p className="text-xl text-slate-600 dark:text-slate-400 max-w-[600px]">
					A comprehensive suite of time tracking and productivity components built with React and Tailwind
					CSS.
				</p>
				<div className="flex gap-4">
					<Link
						href="/components/timer/modern-timer"
						className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-slate-900 hover:bg-slate-800 dark:bg-slate-100 dark:text-slate-900 dark:hover:bg-slate-200 rounded-lg transition-colors duration-200 gap-2"
					>
						Browse Components
						<ArrowRightIcon className="w-4 h-4" />
					</Link>
					<Link
						href="https://docs.cloc.ai"
						className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-slate-900 dark:text-slate-100 border border-slate-200 dark:border-slate-800 hover:bg-slate-100 dark:hover:bg-slate-800/50 rounded-lg transition-colors duration-200"
					>
						Documentation
					</Link>
				</div>
			</div>

			{/* Features Grid */}
			<div className="container mx-auto px-4 py-24 grid grid-cols-1 md:grid-cols-3 gap-8">
				<div className="space-y-4">
					<h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">Time Tracking</h3>
					<p className="text-slate-600 dark:text-slate-400">
						Comprehensive suite of time tracking components from basic timers to advanced progress
						indicators.
					</p>
				</div>
				<div className="space-y-4">
					<h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">Data Visualization</h3>
					<p className="text-slate-600 dark:text-slate-400">
						Beautiful charts and graphs to visualize productivity data and team performance.
					</p>
				</div>
				<div className="space-y-4">
					<h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100">Team Management</h3>
					<p className="text-slate-600 dark:text-slate-400">
						Components for managing team members, tracking progress, and monitoring activities.
					</p>
				</div>
			</div>
			{/* Component Preview */}
			<div className="container mx-auto px-4 py-24 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
				<div className="space-y-6">
					<h2 className="text-4xl font-bold text-slate-900 dark:text-slate-100">Modern Design System</h2>
					<p className="text-lg text-slate-600 dark:text-slate-400">
						Built with modern web technologies and best practices. Fully customizable, accessible, and dark
						mode ready.
					</p>
					<ul className="space-y-4 text-slate-600 dark:text-slate-400">
						<li className="flex items-center gap-2">
							<svg
								className="w-5 h-5 text-green-500"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
							</svg>
							Responsive and accessible components
						</li>
						<li className="flex items-center gap-2">
							<svg
								className="w-5 h-5 text-green-500"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
							</svg>
							Dark mode support out of the box
						</li>
						<li className="flex items-center gap-2">
							<svg
								className="w-5 h-5 text-green-500"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
							</svg>
							Built with TypeScript and React
						</li>
					</ul>
				</div>

				<div className="space-y-4  bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 backdrop-blur-sm rounded-xl p-8">
					<div className="flex items-center gap-2">
						<h3 className="text-sm font-medium text-slate-700 dark:text-slate-400">Try the theme:</h3>
						<ClocThemeToggle />
					</div>

					<div>
						<ModernCloc
							variant="bordered"
							size="lg"
							expanded={true}
							showProgress={true}
							draggable={false}
							resizable={false}
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
