'use client';

import { ThemeProvider } from '@/components/theme-provider';
import { Sidebar } from '@/components/sidebar';
import { TopBar } from '@/components/top-bar';
import { ClocProvider } from '@cloc/atoms';
import { Progress } from '@cloc/ui';

export default function ClientLayout({ children }: { children: React.ReactNode }) {
	return (
		<ThemeProvider attribute="class" defaultTheme="system" enableSystem>
			<ClocProvider>
				<div className="flex min-h-screen bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
					<Sidebar />
					<div className="flex-1 flex flex-col">
						<TopBar />
						<main className="flex-1 p-8 overflow-y-auto">
							<div className="mx-auto max-w-7xl">
								<div className="rounded-xl border border-slate-200 bg-white/50 backdrop-blur-xl dark:border-slate-800 dark:bg-slate-900/50 p-8">
									{children}
								</div>
							</div>
						</main>
					</div>
				</div>
			</ClocProvider>
		</ThemeProvider>
	);
}
