'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@cloc/ui';
import { CodeBlock } from './code-block';
import React from 'react';

interface ComponentExampleProps {
  title: string;
  code: string;
  children: React.ReactNode;
}

export const ComponentExample = ({ title, code, children }: ComponentExampleProps) => {
  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-sm font-medium text-slate-500 dark:text-slate-400">{title}</h3>
      <Tabs defaultValue="preview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="code">Code</TabsTrigger>
        </TabsList>
        <TabsContent value="preview">
          <div className="flex items-center gap-4 p-4 rounded-lg">
            {children}
          </div>
        </TabsContent>
        <TabsContent value="code">
          <CodeBlock code={code} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
