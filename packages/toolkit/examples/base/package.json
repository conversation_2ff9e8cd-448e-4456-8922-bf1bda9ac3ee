{"name": "@cloc/example-base", "version": "0.1.0", "license": "MIT", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@cloc/atoms": "*", "@cloc/ui": "*", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@types/prismjs": "^1.26.5", "next": "^15.2.4", "next-themes": "^0.3.0", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "shiki": "^1.26.1", "theme-ui": "^0.16.2"}, "devDependencies": {"@cloc/eslint-config": "*", "@cloc/tailwind-config": "*", "@cloc/typescript-config": "*", "@next/eslint-plugin-next": "^14.2.3", "@types/node": "^20.11.24", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.18", "eslint": "9.17.0", "eslint-config-next": "15.1.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "packageManager": "yarn@1.22.19"}