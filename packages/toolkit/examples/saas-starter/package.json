{"name": "@cloc/example-saas-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:setup": "npx tsx lib/db/setup.ts", "db:seed": "npx tsx lib/db/seed.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"next": "15.2.4", "next-intl": "^4.1.0", "react": "19.1.0", "react-dom": "19.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.13.9", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@stripe/stripe-js": "^7.3.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.5", "drizzle-orm": "^0.40.0", "jose": "^6.0.8", "lucide-react": "^0.479.0", "postcss": "^8.5.3", "postgres": "^3.4.5", "server-only": "^0.0.1", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "2.0.0", "typescript": "^5.8.2", "zod": "^3.24.2", "@cloc/atoms": "*", "@cloc/ui": "*", "@cloc/api": "*"}}