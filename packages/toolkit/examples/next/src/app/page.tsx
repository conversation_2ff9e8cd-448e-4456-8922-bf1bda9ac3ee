'use client';

import {
	<PERSON>locLoginDialog,
	ClocProjectsList,
	ClocRegistrationForm,
	ClocThemeToggle,
	DailyActivityDisplayer,
	useClocContext,
	WeeklyActivityDisplayer,
	WorkedProjectDisplayer,
	ClocTasksList,
	ClocAppsUrlList,
	BasicClocReport,
	ModernCloc,
	DailyWorkedTimeDisplayer,
	WeeklyWorkedTimeDisplayer
} from '@cloc/atoms';
import { Button, Dialog, ThemedButton } from '@cloc/ui';
import { Suspense } from 'react';

export default function Home() {
	const { authenticatedUser: user } = useClocContext();

	return (
		<div className="p-8">
			<div className="my-20 flex flex-col gap-6 text-xl items-center ">
				<h1 className=" font-bold text-center text-6xl tracking-tighter">Cloc NextJs Boilerplate</h1>
				<p className="text-center text-[#777777] dark:text-gray-400">
					Discover Cloc NextJs Boilerplate and themes to jumpstart your application or website build.
				</p>

				<ClocThemeToggle />

				<div className={'flex gap-3 items-center'}>
					<DailyWorkedTimeDisplayer />
					<WeeklyWorkedTimeDisplayer />
					<WeeklyActivityDisplayer />
					<DailyActivityDisplayer />
					<WorkedProjectDisplayer />
				</div>
				{!user && (
					<div className="flex gap-6">
						<Dialog
							trigger={
								<ThemedButton size={'lg'} className="min-w-40">
									Register Now
								</ThemedButton>
							}
						>
							<ClocRegistrationForm />
						</Dialog>

						<ClocLoginDialog
							trigger={
								<Button
									size={'lg'}
									variant={'outline'}
									className="relative min-w-40 hover:scale-105 transition-all"
								>
									Login
								</Button>
							}
						/>
					</div>
				)}
			</div>
			<div className="flex m-5 box-border gap-2 flex-wrap  justify-center sm:items-start">
				<ModernCloc expanded={false} showProgress={true} />
				<BasicClocReport type="bar" className="max-w-md shadow-none" size={'lg'} />
				<Suspense fallback={<div>Loading...</div>}>
					<ModernCloc expanded showProgress />
				</Suspense>
				<ClocProjectsList />
				<ClocTasksList />
				<ClocAppsUrlList />
			</div>
		</div>
	);
}
