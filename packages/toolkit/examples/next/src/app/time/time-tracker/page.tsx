'use client';
import {
	BasicTimer,
	BasicTimerBorder,
	BasicTimerBorderFullRounded,
	BasicTimerBorderRounded,
	BasicTimerContained,
	BasicTimerContainedFullRounded,
	BasicTimerContainedRounded,
	BasicTimerGray,
	BasicTimerGrayFullRounded,
	BasicTimerGrayRounded,
	BasicTimerIconGray,
	BasicTimerIconGrayFullRounded,
	BasicTimerIconGrayProgress,
	BasicTimerIconGrayProgressButton,
	BasicTimerIconGrayRounded,
	BasicTimerIconGrayRoundedProgress,
	ClocBasic,
	ClocBasicBorder,
	ClocBasicBorderFullRounded,
	ClocBasicBorderRounded,
	ClocBasicContained,
	ClocBasicContainedFullRounded,
	ClocBasicContainedRounded,
	ClocBasicGray,
	ClocBasicGrayFullRounded,
	ClocBasicGrayRounded,
	ModernCloc,
	useClocContext
} from '@cloc/atoms';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@cloc/ui';
import { useEffect } from 'react';

const CarouselItems = [
	[
		<ModernCloc key="modern-cloc-1" expanded={false} showProgress={false} size={'sm'} />,
		<ModernCloc key="modern-cloc-2" expanded={false} showProgress={true} size={'sm'} />,
		<ModernCloc key="modern-cloc-3" expanded={false} showProgress={true} />,
		<ModernCloc key="modern-cloc-4" expanded={false} showProgress={true} variant={'bordered'} />
	],
	[
		<BasicTimer key="basic-timer" />,
		<BasicTimerBorder key="basic-timer-border" />,
		<BasicTimerBorderFullRounded key="basic-timer-border-full-rounded" />,
		<BasicTimerBorderRounded key="basic-timer-border-rounded" />
	],
	[
		<BasicTimerContained key="basic-timer-contained" />,
		<BasicTimerContainedFullRounded key="basic-timer-contained-full-rounded" />,
		<BasicTimerContainedRounded key="basic-timer-contained-rounded" />
	],
	[
		<BasicTimerGray key="basic-timer-gray" />,
		<BasicTimerGrayFullRounded key="basic-timer-gray-full-rounded" />,
		<BasicTimerGrayRounded key="basic-timer-gray-rounded" />,
		<BasicTimerIconGray key="basic-timer-icon-gray" />,
		<BasicTimerIconGrayFullRounded key="basic-timer-icon-gray-full-rounded" />,
		<BasicTimerIconGrayProgress key="basic-timer-icon-gray-progress" />,
		<BasicTimerIconGrayProgressButton key="basic-timer-icon-gray-progress-button" />,
		<BasicTimerIconGrayRounded key="basic-timer-icon-gray-rounded" />,
		<BasicTimerIconGrayRoundedProgress key="basic-timer-icon-gray-rounded-progress" />
	],
	[
		<ClocBasic key="cloc-basic" />,
		<ClocBasicBorder key="cloc-basic-border" />,
		<ClocBasicBorderRounded key="cloc-basic-border-rounded" />,
		<ClocBasicBorderFullRounded key="cloc-basic-border-full-rounded" />,
		<ClocBasicGray key="cloc-basic-gray" />,
		<ClocBasicGrayRounded key="cloc-basic-gray-rounded" />,
		<ClocBasicGrayFullRounded key="cloc-basic-gray-full-rounded" />,
		<ClocBasicContained key="cloc-basic-contained" />,
		<ClocBasicContainedRounded key="cloc-basic-contained-rounded" />,
		<ClocBasicContainedFullRounded key="cloc-basic-contained-full-rounded" />
	]
];

const Page = () => {
	const { authenticatedUser: user } = useClocContext();

	useEffect(() => {
		if (!user) {
			window.location.href = '/';
		}
	}, [user]);

	return (
		<div className="min-h-[65vh] gap-10  flex flex-col my-10 justify-center items-center">
			<div className=" flex flex-col gap-6 text-xl items-center">
				<h1 className=" font-bold text-center text-6xl tracking-tighter">Start Tracking Time Now</h1>
				<p className="text-center text-[#777777] dark:text-gray-400">
					Discover Cloc NextJs Boilerplate and themes to jumpstart your application or website build.
				</p>
				{/* <ClocThemeToggle /> */}
			</div>
			<Carousel
				opts={{
					align: 'start',
					loop: true
				}}
				className="max-w-[80vw] w-fit"
			>
				<CarouselContent>
					{CarouselItems.map((items, index) => (
						<CarouselItem className="flex gap-6 items-center justify-center flex-wrap" key={index}>
							{items}
						</CarouselItem>
					))}
				</CarouselContent>
				<CarouselPrevious />
				<CarouselNext />
			</Carousel>
		</div>
	);
};

export default Page;
