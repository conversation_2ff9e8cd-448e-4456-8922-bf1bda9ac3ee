import React, { useEffect, useRef, useState } from 'react';
import { Data } from 'clarity-decode';
import { Visualizer } from 'clarity-visualize';

interface ClarityClickOverlayProps {
	decodedPayloads: Data.DecodedPayload[];
	className?: string;
}

interface ClickMarker {
	x: number;
	y: number;
	count: number;
}

const ClarityClickOverlay: React.FC<ClarityClickOverlayProps> = ({ decodedPayloads, className }) => {
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const visualizerRef = useRef<Visualizer | null>(null);
	const [hasLoaded, setHasLoaded] = useState(false);
	const [markers, setMarkers] = useState<ClickMarker[]>([]);

	// Render initial DOM only
	useEffect(() => {
		if (!iframeRef.current) return;
		if (!visualizerRef.current) {
			visualizerRef.current = new Visualizer();
		}
		const visualizer = visualizerRef.current;
		const iframe = iframeRef.current;
		const merged = visualizer.merge(decodedPayloads);
		iframe.onload = () => {
			visualizer.setup(iframe.contentWindow as Window, {
				version: 'dev',
				onresize: () => {},
				metadata: undefined,
				mobile: false,
				vNext: true,
				locale: 'en-us',
				onclickMismatch: () => {}
			});
			visualizer.dom(merged.dom);
			setHasLoaded(true);
		};
		// Force reload iframe to trigger onload
		iframe.srcdoc = '<!DOCTYPE html><html><head></head><body></body></html>';
	}, [decodedPayloads]);

	// Extract click events and aggregate by position
	useEffect(() => {
		// Merge all events from all payloads
		const allEvents: Data.DecodedEvent[] = decodedPayloads.flatMap((p) => (p.events as Data.DecodedEvent[]) || []);
		// Filter click events (type === 'click')
		const clickEvents = allEvents.filter((e) => e.type === 'click');
		// Aggregate by rounded position (x, y)
		const markerMap = new Map<string, ClickMarker>();
		clickEvents.forEach((e) => {
			// Some events may have e.data.x, e.data.y or e.x, e.y
			const x = e.data && typeof e.data.x === 'number' ? e.data.x : (e.x ?? null);
			const y = e.data && typeof e.data.y === 'number' ? e.data.y : (e.y ?? null);
			if (typeof x === 'number' && typeof y === 'number') {
				// Round to nearest 5px for aggregation
				const rx = Math.round(x / 5) * 5;
				const ry = Math.round(y / 5) * 5;
				const key = `${rx},${ry}`;
				if (markerMap.has(key)) {
					markerMap.get(key)!.count += 1;
				} else {
					markerMap.set(key, { x: rx, y: ry, count: 1 });
				}
			}
		});
		setMarkers(Array.from(markerMap.values()));
	}, [decodedPayloads]);

	// Overlay rendering: position markers absolutely over the iframe
	// The overlay div should be positioned absolutely and pointer-events: none
	return (
		<div className={className} style={{ position: 'relative', width: '100%', height: '100%' }}>
			<iframe
				ref={iframeRef}
				title="Clarity Initial DOM"
				className="w-full h-full rounded-lg"
				style={{ width: '100%', height: '100%', pointerEvents: 'auto' }}
				scrolling="no"
				sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation"
				allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
			/>
			{hasLoaded && (
				<div
					style={{
						position: 'absolute',
						top: 0,
						left: 0,
						width: '100%',
						height: '100%',
						pointerEvents: 'none',
						zIndex: 10
					}}
				>
					{markers.map((marker, idx) => (
						<div
							key={idx}
							style={{
								position: 'absolute',
								left: marker.x,
								top: marker.y,
								transform: 'translate(-50%, -50%)',
								background: 'rgba(255,0,0,0.7)',
								color: 'white',
								borderRadius: '50%',
								width: 32,
								height: 32,
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
								fontWeight: 'bold',
								fontSize: 14,
								boxShadow: '0 0 8px rgba(0,0,0,0.2)',
								pointerEvents: 'none',
								border: '2px solid white'
							}}
						>
							{marker.count}
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default ClarityClickOverlay;
