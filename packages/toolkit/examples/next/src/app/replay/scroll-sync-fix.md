# Scroll Synchronization Fix for ClarityHeatmap

## Problem Description

The ClarityHeatmap component was experiencing a scrolling synchronization issue where the heatmap canvas overlay remained in its original position when users scrolled within the iframe content. This caused click markers to become misaligned with their actual positions on the page.

## Root Cause

The canvas overlay was positioned absolutely relative to the container but did not account for the scroll position of the iframe content. When the iframe content scrolled, the canvas remained stationary, causing a visual disconnect between the heatmap markers and their corresponding page elements.

## Solution Implementation

### 1. Scroll Position Tracking

Added state to track the iframe's scroll position:
```typescript
const [scrollOffset, setScrollOffset] = useState({ x: 0, y: 0 });
const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
```

### 2. Scroll Event Listener

Implemented a scroll event listener that monitors the iframe's scroll position:
```typescript
useEffect(() => {
  if (!hasLoaded || !iframeRef.current) return;

  const iframe = iframeRef.current;
  const iframeWindow = iframe.contentWindow;
  const iframeDocument = iframe.contentDocument;

  if (!iframeWindow || !iframeDocument) {
    console.warn('ClarityHeatmap: Iframe content not accessible for scroll synchronization');
    return;
  }

  const handleScroll = () => {
    try {
      const scrollX = iframeWindow.scrollX || iframeDocument.documentElement.scrollLeft || 0;
      const scrollY = iframeWindow.scrollY || iframeDocument.documentElement.scrollTop || 0;
      
      // Throttle scroll updates for better performance
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      scrollTimeoutRef.current = setTimeout(() => {
        setScrollOffset({ x: scrollX, y: scrollY });
      }, 16); // ~60fps throttling
    } catch (error) {
      console.warn('ClarityHeatmap: Error reading scroll position:', error);
    }
  };

  // Add scroll event listener to iframe
  iframeWindow.addEventListener('scroll', handleScroll, { passive: true });
  
  // Initial scroll position
  handleScroll();
  
  console.log('ClarityHeatmap: Scroll synchronization enabled');

  // Cleanup
  return () => {
    if (iframeWindow) {
      iframeWindow.removeEventListener('scroll', handleScroll);
    }
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
  };
}, [hasLoaded]);
```

### 3. Canvas Rendering with Scroll Compensation

Updated the canvas rendering to adjust heatmap point positions based on scroll offset:
```typescript
// Draw heatmap points with scroll offset compensation
heatmapPoints.forEach((point) => {
  // Adjust point position based on scroll offset
  const adjustedX = point.x - scrollOffset.x;
  const adjustedY = point.y - scrollOffset.y;

  // Only draw points that are visible in the current viewport
  if (adjustedX < -50 || adjustedX > canvas.width + 50 || 
      adjustedY < -50 || adjustedY > canvas.height + 50) {
    return; // Skip points outside visible area for performance
  }

  // ... rest of rendering code using adjustedX, adjustedY
});
```

### 4. Performance Optimizations

#### Throttling
- Implemented 16ms throttling (~60fps) for scroll updates to prevent excessive re-renders
- Used `setTimeout` to batch scroll position updates

#### Viewport Culling
- Added viewport culling to only render heatmap points that are visible in the current view
- 50px buffer zone to handle points near the edges smoothly

#### Passive Event Listeners
- Used `{ passive: true }` for scroll event listeners to improve performance

## Key Features

### ✅ **Scroll Synchronization**
- Heatmap overlay now moves perfectly with iframe content
- Supports both vertical and horizontal scrolling
- Real-time position updates during scroll operations

### ✅ **Performance Optimized**
- 60fps throttling prevents excessive re-renders
- Viewport culling improves performance with large datasets
- Passive event listeners for better scroll performance

### ✅ **Error Handling**
- Graceful handling of iframe access issues
- Console warnings for debugging
- Proper cleanup of event listeners and timeouts

### ✅ **Cross-browser Compatibility**
- Fallback scroll position detection for different browsers
- Handles both `scrollX/scrollY` and `documentElement.scrollLeft/scrollTop`

## Testing

### Manual Testing Steps
1. Load a session with click data in the heatmap view
2. Scroll the page content within the iframe
3. Verify that heatmap markers move with the content
4. Test both vertical and horizontal scrolling
5. Confirm performance remains smooth during rapid scrolling

### Expected Behavior
- ✅ Heatmap markers stay aligned with their page elements during scroll
- ✅ Smooth scrolling performance without lag
- ✅ No visual artifacts or misalignment
- ✅ Console logs confirm scroll synchronization is enabled

## Technical Details

### Browser Compatibility
- Works with modern browsers that support iframe content access
- Handles cross-origin restrictions gracefully
- Fallback scroll position detection for older browsers

### Performance Characteristics
- ~60fps update rate for smooth visual experience
- Minimal CPU overhead with throttling and viewport culling
- Memory efficient with proper cleanup

### Security Considerations
- Respects iframe sandbox restrictions
- Graceful degradation when iframe content is not accessible
- No security vulnerabilities introduced

## Future Enhancements

### Potential Improvements
1. **Zoom Synchronization**: Handle iframe zoom level changes
2. **Resize Handling**: Better handling of iframe size changes
3. **Touch Scrolling**: Enhanced support for mobile touch scrolling
4. **Smooth Interpolation**: Smoother transitions during rapid scrolling

### Integration Opportunities
1. **Real-time Updates**: Live scroll synchronization for real-time sessions
2. **Multi-viewport**: Support for multiple iframe views
3. **Advanced Culling**: More sophisticated viewport culling algorithms

## Conclusion

The scroll synchronization fix successfully resolves the alignment issue between heatmap markers and page content. The implementation is performant, robust, and maintains the existing functionality while adding seamless scroll tracking capabilities.

The solution provides a solid foundation for advanced heatmap visualizations and demonstrates the feasibility of building sophisticated user interaction analytics tools with the Clarity session replay infrastructure.
