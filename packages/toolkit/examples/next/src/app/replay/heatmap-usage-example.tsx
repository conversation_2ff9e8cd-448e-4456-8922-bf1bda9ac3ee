/**
 * ClarityHeatmap Usage Examples
 *
 * This file demonstrates different ways to use the ClarityHeatmap component
 * with various configuration options and use cases.
 */

import React from 'react';
import { Data } from 'clarity-decode';
import ClarityHeatmap, { ClarityHeatmapProps } from './clarity-heatmap';

// Example 1: Basic Usage
export const BasicHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ decodedPayloads }) => {
	return (
		<div className="w-full h-[600px]">
			<ClarityHeatmap decodedPayloads={decodedPayloads} />
		</div>
	);
};

// Example 2: Customized Heatmap with Different Color Scheme
export const CoolHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ decodedPayloads }) => {
	return (
		<div className="w-full h-[600px]">
			<ClarityHeatmap
				decodedPayloads={decodedPayloads}
				colorScheme="cool"
				aggregationRadius={20}
				showCounts={false}
				className="custom-heatmap"
			/>
		</div>
	);
};

// Example 3: High-Precision Heatmap (smaller aggregation radius)
export const HighPrecisionHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({
	decodedPayloads
}) => {
	return (
		<div className="w-full h-[600px]">
			<ClarityHeatmap
				decodedPayloads={decodedPayloads}
				colorScheme="blue"
				aggregationRadius={5} // Very precise clustering
				showCounts={true}
			/>
		</div>
	);
};

// Example 4: Multiple Heatmap Comparison
export const HeatmapComparisonExample: React.FC<{
	sessionA: Data.DecodedPayload[];
	sessionB: Data.DecodedPayload[];
}> = ({ sessionA, sessionB }) => {
	return (
		<div className="flex gap-5 h-[600px]">
			<div className="flex-1 space-y-3">
				<h3 className="text-lg font-semibold text-gray-900 dark:text-white">Session A</h3>
				<div className="h-full">
					<ClarityHeatmap decodedPayloads={sessionA} colorScheme="hot" aggregationRadius={15} />
				</div>
			</div>
			<div className="flex-1 space-y-3">
				<h3 className="text-lg font-semibold text-gray-900 dark:text-white">Session B</h3>
				<div className="h-full">
					<ClarityHeatmap decodedPayloads={sessionB} colorScheme="cool" aggregationRadius={15} />
				</div>
			</div>
		</div>
	);
};

// Example 5: Heatmap with Custom Styling
export const StyledHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ decodedPayloads }) => {
	return (
		<div className="w-full h-[600px] border-2 border-slate-200 rounded-xl shadow-xl overflow-hidden">
			<ClarityHeatmap
				decodedPayloads={decodedPayloads}
				colorScheme="hot"
				aggregationRadius={12}
				showCounts={true}
			/>
		</div>
	);
};

// Example 6: Responsive Heatmap Container
export const ResponsiveHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ decodedPayloads }) => {
	return (
		<div className="w-full h-[80vh] min-h-[400px] max-h-[800px] resize overflow-hidden border border-gray-300 rounded-lg">
			<ClarityHeatmap
				decodedPayloads={decodedPayloads}
				aggregationRadius={10}
				showCounts={true}
				colorScheme="hot"
			/>
		</div>
	);
};

// Example 7: Heatmap Configuration Options
export const heatmapConfigurations: ClarityHeatmapProps[] = [
	{
		decodedPayloads: [], // Would be populated with actual data
		colorScheme: 'hot',
		aggregationRadius: 10,
		showCounts: true
	},
	{
		decodedPayloads: [],
		colorScheme: 'cool',
		aggregationRadius: 15,
		showCounts: false
	},
	{
		decodedPayloads: [],
		colorScheme: 'blue',
		aggregationRadius: 20,
		showCounts: true
	}
];

// Example 8: Integration with State Management
export const StatefulHeatmapExample: React.FC = () => {
	const [decodedPayloads, setDecodedPayloads] = React.useState<Data.DecodedPayload[]>([]);
	const [colorScheme, setColorScheme] = React.useState<'hot' | 'cool' | 'blue'>('hot');
	const [aggregationRadius, setAggregationRadius] = React.useState(15);
	const [showCounts, setShowCounts] = React.useState(true);

	return (
		<div>
			{/* Controls */}
			<div style={{ marginBottom: '20px', padding: '10px', background: '#f5f5f5' }}>
				<label>
					Color Scheme:
					<select
						value={colorScheme}
						onChange={(e) => setColorScheme(e.target.value as any)}
						style={{ marginLeft: '10px' }}
					>
						<option value="hot">Hot (Red-Yellow)</option>
						<option value="cool">Cool (Blue-Cyan)</option>
						<option value="blue">Blue</option>
					</select>
				</label>

				<label style={{ marginLeft: '20px' }}>
					Aggregation Radius:
					<input
						type="range"
						min="5"
						max="30"
						value={aggregationRadius}
						onChange={(e) => setAggregationRadius(Number(e.target.value))}
						style={{ marginLeft: '10px' }}
					/>
					<span style={{ marginLeft: '5px' }}>{aggregationRadius}px</span>
				</label>

				<label style={{ marginLeft: '20px' }}>
					<input type="checkbox" checked={showCounts} onChange={(e) => setShowCounts(e.target.checked)} />
					Show Click Counts
				</label>
			</div>

			{/* Heatmap */}
			<div style={{ width: '100%', height: '600px' }}>
				<ClarityHeatmap
					decodedPayloads={decodedPayloads}
					colorScheme={colorScheme}
					aggregationRadius={aggregationRadius}
					showCounts={showCounts}
				/>
			</div>
		</div>
	);
};

// Example 9: Error Handling Wrapper
export const SafeHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ decodedPayloads }) => {
	const [error, setError] = React.useState<string | null>(null);

	React.useEffect(() => {
		setError(null);

		if (!decodedPayloads || decodedPayloads.length === 0) {
			setError('No session data provided');
			return;
		}

		// Validate payloads have required structure
		const hasValidPayloads = decodedPayloads.some((payload) => payload.envelope && payload.envelope.sessionId);

		if (!hasValidPayloads) {
			setError('Invalid session data format');
			return;
		}
	}, [decodedPayloads]);

	if (error) {
		return (
			<div
				style={{
					padding: '20px',
					textAlign: 'center',
					color: '#666',
					border: '1px dashed #ccc',
					borderRadius: '8px'
				}}
			>
				<div>⚠️ {error}</div>
			</div>
		);
	}

	return (
		<div style={{ width: '100%', height: '600px' }}>
			<ClarityHeatmap decodedPayloads={decodedPayloads} />
		</div>
	);
};
