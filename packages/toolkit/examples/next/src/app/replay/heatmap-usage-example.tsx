/**
 * ClarityHeatmap Usage Examples
 * 
 * This file demonstrates different ways to use the ClarityHeatmap component
 * with various configuration options and use cases.
 */

import React from 'react';
import { Data } from 'clarity-decode';
import ClarityHeatmap, { ClarityHeatmapProps } from './clarity-heatmap';

// Example 1: Basic Usage
export const BasicHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  return (
    <div style={{ width: '100%', height: '600px' }}>
      <ClarityHeatmap decodedPayloads={decodedPayloads} />
    </div>
  );
};

// Example 2: Customized Heatmap with Different Color Scheme
export const CoolHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  return (
    <div style={{ width: '100%', height: '600px' }}>
      <ClarityHeatmap 
        decodedPayloads={decodedPayloads}
        colorScheme="cool"
        aggregationRadius={20}
        showCounts={false}
        className="custom-heatmap"
      />
    </div>
  );
};

// Example 3: High-Precision Heatmap (smaller aggregation radius)
export const HighPrecisionHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  return (
    <div style={{ width: '100%', height: '600px' }}>
      <ClarityHeatmap 
        decodedPayloads={decodedPayloads}
        colorScheme="blue"
        aggregationRadius={5}  // Very precise clustering
        showCounts={true}
      />
    </div>
  );
};

// Example 4: Multiple Heatmap Comparison
export const HeatmapComparisonExample: React.FC<{ 
  sessionA: Data.DecodedPayload[];
  sessionB: Data.DecodedPayload[];
}> = ({ sessionA, sessionB }) => {
  return (
    <div style={{ display: 'flex', gap: '20px', height: '600px' }}>
      <div style={{ flex: 1 }}>
        <h3>Session A</h3>
        <ClarityHeatmap 
          decodedPayloads={sessionA}
          colorScheme="hot"
          aggregationRadius={15}
        />
      </div>
      <div style={{ flex: 1 }}>
        <h3>Session B</h3>
        <ClarityHeatmap 
          decodedPayloads={sessionB}
          colorScheme="cool"
          aggregationRadius={15}
        />
      </div>
    </div>
  );
};

// Example 5: Heatmap with Custom Styling
export const StyledHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  const customStyles = {
    border: '2px solid #e2e8f0',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden'
  };

  return (
    <div style={{ width: '100%', height: '600px', ...customStyles }}>
      <ClarityHeatmap 
        decodedPayloads={decodedPayloads}
        colorScheme="hot"
        aggregationRadius={12}
        showCounts={true}
      />
    </div>
  );
};

// Example 6: Responsive Heatmap Container
export const ResponsiveHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  return (
    <div style={{ 
      width: '100%', 
      height: '80vh',
      minHeight: '400px',
      maxHeight: '800px',
      resize: 'both',
      overflow: 'hidden',
      border: '1px solid #ccc'
    }}>
      <ClarityHeatmap 
        decodedPayloads={decodedPayloads}
        aggregationRadius={10}
        showCounts={true}
        colorScheme="hot"
      />
    </div>
  );
};

// Example 7: Heatmap Configuration Options
export const heatmapConfigurations: ClarityHeatmapProps[] = [
  {
    decodedPayloads: [], // Would be populated with actual data
    colorScheme: 'hot',
    aggregationRadius: 10,
    showCounts: true
  },
  {
    decodedPayloads: [],
    colorScheme: 'cool',
    aggregationRadius: 15,
    showCounts: false
  },
  {
    decodedPayloads: [],
    colorScheme: 'blue',
    aggregationRadius: 20,
    showCounts: true
  }
];

// Example 8: Integration with State Management
export const StatefulHeatmapExample: React.FC = () => {
  const [decodedPayloads, setDecodedPayloads] = React.useState<Data.DecodedPayload[]>([]);
  const [colorScheme, setColorScheme] = React.useState<'hot' | 'cool' | 'blue'>('hot');
  const [aggregationRadius, setAggregationRadius] = React.useState(15);
  const [showCounts, setShowCounts] = React.useState(true);

  return (
    <div>
      {/* Controls */}
      <div style={{ marginBottom: '20px', padding: '10px', background: '#f5f5f5' }}>
        <label>
          Color Scheme:
          <select 
            value={colorScheme} 
            onChange={(e) => setColorScheme(e.target.value as any)}
            style={{ marginLeft: '10px' }}
          >
            <option value="hot">Hot (Red-Yellow)</option>
            <option value="cool">Cool (Blue-Cyan)</option>
            <option value="blue">Blue</option>
          </select>
        </label>
        
        <label style={{ marginLeft: '20px' }}>
          Aggregation Radius:
          <input 
            type="range" 
            min="5" 
            max="30" 
            value={aggregationRadius}
            onChange={(e) => setAggregationRadius(Number(e.target.value))}
            style={{ marginLeft: '10px' }}
          />
          <span style={{ marginLeft: '5px' }}>{aggregationRadius}px</span>
        </label>
        
        <label style={{ marginLeft: '20px' }}>
          <input 
            type="checkbox" 
            checked={showCounts}
            onChange={(e) => setShowCounts(e.target.checked)}
          />
          Show Click Counts
        </label>
      </div>

      {/* Heatmap */}
      <div style={{ width: '100%', height: '600px' }}>
        <ClarityHeatmap 
          decodedPayloads={decodedPayloads}
          colorScheme={colorScheme}
          aggregationRadius={aggregationRadius}
          showCounts={showCounts}
        />
      </div>
    </div>
  );
};

// Example 9: Error Handling Wrapper
export const SafeHeatmapExample: React.FC<{ decodedPayloads: Data.DecodedPayload[] }> = ({ 
  decodedPayloads 
}) => {
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    setError(null);
    
    if (!decodedPayloads || decodedPayloads.length === 0) {
      setError('No session data provided');
      return;
    }

    // Validate payloads have required structure
    const hasValidPayloads = decodedPayloads.some(payload => 
      payload.envelope && payload.envelope.sessionId
    );
    
    if (!hasValidPayloads) {
      setError('Invalid session data format');
      return;
    }
  }, [decodedPayloads]);

  if (error) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        color: '#666',
        border: '1px dashed #ccc',
        borderRadius: '8px'
      }}>
        <div>⚠️ {error}</div>
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: '600px' }}>
      <ClarityHeatmap decodedPayloads={decodedPayloads} />
    </div>
  );
};
