import React, { useEffect, useRef, useState } from 'react';
import { Data } from 'clarity-decode';
import { Visualizer } from 'clarity-visualize';

interface ClarityHeatmapProps {
	decodedPayloads: Data.DecodedPayload[];
	className?: string;
	aggregationRadius?: number; // Pixel radius for clustering clicks
	showCounts?: boolean; // Whether to show click counts on markers
	colorScheme?: 'hot' | 'cool' | 'blue'; // Color scheme for heatmap
}

interface HeatmapPoint {
	x: number;
	y: number;
	count: number;
	intensity: number; // 0-1 normalized intensity
}

/**
 * ClarityHeatmap - A React component that visualizes click heatmaps from Clarity session replay data
 *
 * This component renders the original page layout and overlays click interaction data as a heatmap.
 * It aggregates click events by coordinate position and displays them with color-coded intensity.
 *
 * Features:
 * - Displays original page layout using Clarity Visualizer
 * - Aggregates clicks by coordinate clustering
 * - Multiple color schemes (hot, cool, blue)
 * - Configurable aggregation radius
 * - Optional click count display
 * - Real-time statistics overlay
 *
 * @param decodedPayloads - Array of decoded Clarity session payloads
 * @param className - Optional CSS class name
 * @param aggregationRadius - Pixel radius for clustering nearby clicks (default: 10)
 * @param showCounts - Whether to show click counts on markers (default: true)
 * @param colorScheme - Color scheme for heatmap visualization (default: 'hot')
 */
const ClarityHeatmap: React.FC<ClarityHeatmapProps> = ({
	decodedPayloads,
	className,
	aggregationRadius = 10,
	showCounts = true,
	colorScheme = 'hot'
}) => {
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const visualizerRef = useRef<Visualizer | null>(null);
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const [hasLoaded, setHasLoaded] = useState(false);
	const [heatmapPoints, setHeatmapPoints] = useState<HeatmapPoint[]>([]);
	const [maxCount, setMaxCount] = useState(0);

	// Color schemes for heatmap visualization
	const getColorForIntensity = (intensity: number): string => {
		const alpha = Math.min(0.8, 0.3 + intensity * 0.5); // Base alpha + intensity scaling

		switch (colorScheme) {
			case 'hot':
				// Red to yellow gradient
				const red = Math.min(255, 150 + intensity * 105);
				const green = Math.min(255, intensity * 200);
				return `rgba(${red}, ${green}, 0, ${alpha})`;
			case 'cool':
				// Blue to cyan gradient
				const blue = Math.min(255, 150 + intensity * 105);
				const cyan = Math.min(255, intensity * 200);
				return `rgba(0, ${cyan}, ${blue}, ${alpha})`;
			case 'blue':
				// Blue gradient
				const blueIntensity = Math.min(255, 100 + intensity * 155);
				return `rgba(0, 100, ${blueIntensity}, ${alpha})`;
			default:
				return `rgba(255, 0, 0, ${alpha})`;
		}
	};

	// Render initial DOM
	useEffect(() => {
		if (!iframeRef.current) return;

		if (!visualizerRef.current) {
			visualizerRef.current = new Visualizer();
		}

		const visualizer = visualizerRef.current;
		const iframe = iframeRef.current;
		const merged = visualizer.merge(decodedPayloads);

		iframe.onload = () => {
			visualizer.setup(iframe.contentWindow as Window, {
				version: 'dev',
				onresize: () => {},
				metadata: undefined,
				mobile: false,
				vNext: true,
				locale: 'en-us',
				onclickMismatch: () => {}
			});
			visualizer.dom(merged.dom);
			setHasLoaded(true);
		};

		// Force reload iframe to trigger onload
		iframe.srcdoc = '<!DOCTYPE html><html><head></head><body></body></html>';
	}, [decodedPayloads]);

	// Extract and aggregate click events
	useEffect(() => {
		if (!decodedPayloads.length || !visualizerRef.current) return;

		// Extract click events from the payloads directly
		const clickEvents: any[] = [];

		decodedPayloads.forEach((payload) => {
			if (payload.click) {
				clickEvents.push(...payload.click);
			}
		});

		console.log(`ClarityHeatmap: Found ${clickEvents.length} click events from ${decodedPayloads.length} payloads`);

		// Aggregate clicks by position with clustering
		const pointMap = new Map<string, HeatmapPoint>();

		clickEvents.forEach((event: any) => {
			// Extract coordinates from click event data
			const x = event.data?.x;
			const y = event.data?.y;

			if (typeof x === 'number' && typeof y === 'number') {
				// Round coordinates to aggregation radius for clustering
				const clusteredX = Math.round(x / aggregationRadius) * aggregationRadius;
				const clusteredY = Math.round(y / aggregationRadius) * aggregationRadius;
				const key = `${clusteredX},${clusteredY}`;

				if (pointMap.has(key)) {
					const existing = pointMap.get(key)!;
					existing.count += 1;
				} else {
					pointMap.set(key, {
						x: clusteredX,
						y: clusteredY,
						count: 1,
						intensity: 0 // Will be calculated after all points are collected
					});
				}
			}
		});

		// Convert to array and calculate intensities
		const points = Array.from(pointMap.values());
		const maxClickCount = Math.max(...points.map((p) => p.count), 1);

		// Normalize intensities
		points.forEach((point) => {
			point.intensity = point.count / maxClickCount;
		});

		setHeatmapPoints(points);
		setMaxCount(maxClickCount);

		console.log(`ClarityHeatmap: Generated ${points.length} heatmap points, max count: ${maxClickCount}`);
	}, [decodedPayloads, aggregationRadius]);

	// Render heatmap canvas overlay
	useEffect(() => {
		if (!hasLoaded || !canvasRef.current || heatmapPoints.length === 0) return;

		const canvas = canvasRef.current;
		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		// Set canvas size to match iframe
		const iframe = iframeRef.current;
		if (iframe) {
			canvas.width = iframe.offsetWidth;
			canvas.height = iframe.offsetHeight;
		}

		// Clear canvas
		ctx.clearRect(0, 0, canvas.width, canvas.height);

		// Draw heatmap points
		heatmapPoints.forEach((point) => {
			const radius = Math.max(8, 4 + point.intensity * 20); // Dynamic radius based on intensity
			const color = getColorForIntensity(point.intensity);

			// Draw gradient circle for heat effect
			const gradient = ctx.createRadialGradient(point.x, point.y, 0, point.x, point.y, radius);
			gradient.addColorStop(0, color);
			gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

			ctx.fillStyle = gradient;
			ctx.beginPath();
			ctx.arc(point.x, point.y, radius, 0, 2 * Math.PI);
			ctx.fill();

			// Draw count text if enabled
			if (showCounts && point.count > 1) {
				ctx.fillStyle = 'white';
				ctx.font = 'bold 12px Arial';
				ctx.textAlign = 'center';
				ctx.textBaseline = 'middle';
				ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
				ctx.lineWidth = 2;
				ctx.strokeText(point.count.toString(), point.x, point.y);
				ctx.fillText(point.count.toString(), point.x, point.y);
			}
		});
	}, [hasLoaded, heatmapPoints, showCounts, colorScheme]);

	return (
		<div className={className} style={{ position: 'relative', width: '100%', height: '100%' }}>
			{/* Base iframe with original page */}
			<iframe
				ref={iframeRef}
				title="Clarity Heatmap Base"
				className="w-full h-full rounded-lg"
				style={{ width: '100%', height: '100%', overflow: 'hidden' }}
				sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation"
				allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
			/>

			{/* Heatmap canvas overlay */}
			{hasLoaded && (
				<canvas
					ref={canvasRef}
					style={{
						position: 'absolute',
						top: 0,
						left: 0,
						width: '100%',
						height: '100%',
						pointerEvents: 'none',
						zIndex: 10
					}}
					className="rounded-lg"
				/>
			)}

			{/* Stats overlay */}
			{hasLoaded && heatmapPoints.length > 0 && (
				<div
					style={{
						position: 'absolute',
						top: 10,
						right: 10,
						background: 'rgba(0, 0, 0, 0.8)',
						color: 'white',
						padding: '8px 12px',
						borderRadius: '6px',
						fontSize: '12px',
						zIndex: 20,
						pointerEvents: 'none'
					}}
				>
					<div>Total Clicks: {heatmapPoints.reduce((sum, p) => sum + p.count, 0)}</div>
					<div>Hotspots: {heatmapPoints.length}</div>
					<div>Max Clicks: {maxCount}</div>
				</div>
			)}

			{/* Loading indicator */}
			{!hasLoaded && (
				<div
					style={{
						position: 'absolute',
						top: '50%',
						left: '50%',
						transform: 'translate(-50%, -50%)',
						background: 'rgba(0, 0, 0, 0.8)',
						color: 'white',
						padding: '12px 16px',
						borderRadius: '6px',
						fontSize: '14px',
						zIndex: 30
					}}
				>
					Loading heatmap...
				</div>
			)}

			{/* No clicks indicator */}
			{hasLoaded && heatmapPoints.length === 0 && (
				<div
					style={{
						position: 'absolute',
						top: '50%',
						left: '50%',
						transform: 'translate(-50%, -50%)',
						background: 'rgba(0, 0, 0, 0.8)',
						color: 'white',
						padding: '12px 16px',
						borderRadius: '6px',
						fontSize: '14px',
						zIndex: 30,
						textAlign: 'center'
					}}
				>
					<div>🔥 No click data found</div>
					<div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
						This session contains no recorded clicks
					</div>
				</div>
			)}
		</div>
	);
};

export default ClarityHeatmap;
export { ClarityHeatmap };
export type { ClarityHeatmapProps, HeatmapPoint };
