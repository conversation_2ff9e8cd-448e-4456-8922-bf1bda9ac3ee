import type { Metada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from 'next/font/google';
import './globals.css';
import ClientLayout from '@/components/layout/client-layout';

const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin']
});

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin']
});

export const metadata: Metadata = {
	title: 'Cloc - NextJs Boilerplate',
	description: 'Generated by create next app'
};

export default function RootLayout({
	children
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html suppressHydrationWarning lang="en" dir="ltr">
			<body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
				<ClientLayout>
					<main id="main-content" role="main">
						{children}
					</main>
				</ClientLayout>
			</body>
		</html>
	);
}
