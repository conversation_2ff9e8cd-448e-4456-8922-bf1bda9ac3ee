{"name": "@cloc/example-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cloc/atoms": "*", "@cloc/tracking": "*", "@cloc/ui": "*", "next": "15.2.4", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "^5.0.0", "clarity-decode": "0.8.19", "clarity-visualize": "0.8.19", "pako": "^2.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/pako": "2.0.3", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "lucide-react": "^0.394.0"}}