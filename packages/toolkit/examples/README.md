# Cloc Kit Examples / Samples

Current examples:
- `/base` - example NextJs app with browse of all Cloc Kit components (can be used instead of Storybook to quickly browse prebuilt Cloc components)
- `/next-boilerplate-ixartz` - example using <https://github.com/ixartz/Next-js-Boilerplate>
- `/next` - bare [NextJs](https://github.com/vercel/next.js) example ("out of the box" NextJs app setup)
- `/remix` - Remix-based example
- `/saas-starter` - example using <https://github.com/nextjs/saas-starter>
- `/vite` - example using Start Vite Template.

Possible candidates for more examples:
- `/saas-starter-kit` - example using <https://github.com/boxyhq/saas-starter-kit>
- `/next-boilerplate-hadrysm` - example using <https://github.com/hadrysm/nextjs-boilerplate>
- `/next-starter` - example using <https://github.com/Skolaczk/next-starter>
- `/next-forge` - full-blown mono-repo example using <https://github.com/vercel/next-forge>
