'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn } from '../utils/utils';
import { Button } from './button';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { IDatePickerProps } from '@cloc/types';

export function DatePicker({
	placeholder = 'Pick a date',
	date: propDate,
	icon = true,
	className,
	...props
}: IDatePickerProps) {
	const [date, setDate] = React.useState<Date | undefined>(propDate);
	const [open, setOpen] = React.useState(false);

	React.useEffect(() => {
		setDate(propDate);
	}, [propDate]);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant={'outline'}
					className={cn(
						'w-[280px] justify-start text-left font-normal',
						!date && 'text-muted-foreground',
						className
					)}
				>
					{icon && <CalendarIcon className="mr-2 h-4 w-4" />}
					{date ? format(date, 'PPP') : <span>{placeholder}</span>}
				</Button>
			</PopoverTrigger>
			<PopoverContent align="start" className="w-auto p-0">
				<Calendar
					{...props}
					mode="single"
					selected={date}
					onSelect={(date) => {
						setDate(date);
						setOpen(false);
					}}
					initialFocus
				/>
			</PopoverContent>
		</Popover>
	);
}
