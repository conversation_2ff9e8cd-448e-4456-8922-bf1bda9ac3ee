import { useEffect, useState, useRef, ReactElement } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '../popover';
import { Button } from '../button';
import { cn } from '@/lib/utils/utils';

interface MultiSelectProps<T> {
	items: T[];
	onValueChange?: (value: T | T[] | null) => void;
	itemToString: (item: T) => string;
	itemId: (item: T) => string;
	triggerClassName?: string;
	popoverClassName?: string;
	renderItem?: (item: T, onClick: () => void, isSelected: boolean) => ReactElement;
	defaultValue?: T | T[];
	multiSelect?: boolean;
}

export function MultiSelect<T>({
	items,
	onValueChange,
	itemToString,
	itemId,
	triggerClassName = '',
	popoverClassName = '',
	renderItem,
	defaultValue,
	multiSelect = false
}: MultiSelectProps<T>) {
	const [selectedItems, setSelectedItems] = useState<T[]>(
		Array.isArray(defaultValue) ? defaultValue : defaultValue ? [defaultValue] : []
	);
	const [isPopoverOpen, setPopoverOpen] = useState(false);
	const [popoverWidth, setPopoverWidth] = useState<number | null>(null);
	const triggerRef = useRef<HTMLButtonElement>(null);

	const onClick = (item: T) => {
		let newSelectedItems: T[];
		if (multiSelect) {
			if (selectedItems.some((selectedItem) => itemId(selectedItem) === itemId(item))) {
				newSelectedItems = selectedItems.filter((selectedItem) => itemId(selectedItem) !== itemId(item));
			} else {
				newSelectedItems = [...selectedItems, item];
			}
		} else {
			newSelectedItems = [item];
			setPopoverOpen(false);
		}
		setSelectedItems(newSelectedItems);
		if (onValueChange) {
			onValueChange(multiSelect ? newSelectedItems : newSelectedItems[0]);
		}
	};

	const removeItem = (item: T) => {
		const newSelectedItems = selectedItems.filter((selectedItem) => itemId(selectedItem) !== itemId(item));
		setSelectedItems(newSelectedItems);
		if (onValueChange) {
			onValueChange(multiSelect ? newSelectedItems : newSelectedItems.length > 0 ? newSelectedItems[0] : null);
		}
	};

	useEffect(() => {
		const initialItems = Array.isArray(defaultValue) ? defaultValue : defaultValue ? [defaultValue] : [];
		setSelectedItems(initialItems);
		if (onValueChange) {
			onValueChange(multiSelect ? initialItems : initialItems[0] || null);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [defaultValue]);

	useEffect(() => {
		if (triggerRef.current) {
			setPopoverWidth(triggerRef.current.offsetWidth);
		}
	}, [triggerRef.current]);

	return (
		<div className="relative w-full overflow-hidden">
			<Popover open={isPopoverOpen} onOpenChange={setPopoverOpen}>
				<PopoverTrigger asChild>
					<Button
						ref={triggerRef}
						onClick={() => setPopoverOpen(!isPopoverOpen)}
						variant="outline"
						className={cn(
							'w-full justify-between text-left font-normal h-10 rounded-lg dark:bg-dark--theme-light',
							triggerClassName
						)}
					>
						{selectedItems.length > 0 ? (
							<span className="truncate text-slate-800 dark:text-slate-400">
								{multiSelect
									? `${selectedItems.length} item(s) selected`
									: itemToString(selectedItems[0])}
							</span>
						) : (
							<span className="text-slate-800 dark:text-white">Select items</span>
						)}

						<svg
							className={cn('h-4 w-4 transition-transform', isPopoverOpen && 'rotate-180')}
							width="15"
							height="15"
							viewBox="0 0 15 15"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								d="M4.18179 6.18181C4.35753 6.00608 4.64245 6.00608 4.81819 6.18181L7.49999 8.86362L10.1818 6.18181C10.3575 6.00608 10.6424 6.00608 10.8182 6.18181C10.9939 6.35755 10.9939 6.64247 10.8182 6.81821L7.81819 9.81821C7.73379 9.9026 7.61934 9.95001 7.49999 9.95001C7.38064 9.95001 7.26618 9.9026 7.18179 9.81821L4.18179 6.81821C4.00605 6.64247 4.00605 6.35755 4.18179 6.18181Z"
								fill="currentColor"
								fillRule="evenodd"
								clipRule="evenodd"
							></path>
						</svg>
						{/* <MdOutlineKeyboardArrowDown
                            className={cn('h-4 w-4 transition-transform', isPopoverOpen && 'rotate-180')}
                        /> */}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					className={cn(
						'w-full max-w-full max-h-[80vh] border border-transparent dark:bg-dark--theme-light',
						popoverClassName
					)}
					style={{ width: popoverWidth || 'auto', overflow: 'auto' }}
				>
					<div className="w-full max-h-[80vh] overflow-auto flex flex-col">
						{items.map((item) => {
							const isSelected = selectedItems.some(
								(selectedItem) => itemId(selectedItem) === itemId(item)
							);
							return renderItem ? (
								renderItem(item, () => onClick(item), isSelected)
							) : (
								<span
									onClick={() => onClick(item)}
									key={itemId(item)}
									className={cn(
										'truncate hover:cursor-pointer hover:bg-slate-50 w-full text-[13px] hover:rounded-lg p-1 hover:font-normal dark:text-white dark:hover:bg-primary',
										isSelected && 'font-semibold bg-slate-100 dark:bg-primary-light'
									)}
									style={{ textOverflow: 'ellipsis', whiteSpace: 'nowrap', overflow: 'hidden' }}
								>
									{itemToString(item)}
								</span>
							);
						})}
					</div>
				</PopoverContent>
			</Popover>
			{selectedItems.length > 0 && (
				<div className="mt-2 flex flex-wrap gap-2">
					{selectedItems.map((item) => (
						<div
							style={{ backgroundColor: '#f3f4f6' }}
							key={itemId(item)}
							className="flex items-center justify-between !bg-gray-100 dark:bg-slate-700 px-2 py-[0.5px] rounded text-[12px] text-slate-800"
						>
							<span>{itemToString(item)}</span>
							<button
								onClick={() => removeItem(item)}
								className="ml-2 text-slate-800 hover:text-red-500 dark:hover:text-red-500"
								aria-label="Remove item"
							>
								<svg
									className="h-4 w-4"
									width="15"
									height="15"
									viewBox="0 0 15 15"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M12.8536 2.85355C13.0488 2.65829 13.0488 2.34171 12.8536 2.14645C12.6583 1.95118 12.3417 1.95118 12.1464 2.14645L7.5 6.79289L2.85355 2.14645C2.65829 1.95118 2.34171 1.95118 2.14645 2.14645C1.95118 2.34171 1.95118 2.65829 2.14645 2.85355L6.79289 7.5L2.14645 12.1464C1.95118 12.3417 1.95118 12.6583 2.14645 12.8536C2.34171 13.0488 2.65829 13.0488 2.85355 12.8536L7.5 8.20711L12.1464 12.8536C12.3417 13.0488 12.6583 13.0488 12.8536 12.8536C13.0488 12.6583 13.0488 12.3417 12.8536 12.1464L8.20711 7.5L12.8536 2.85355Z"
										fill="currentColor"
										fillRule="evenodd"
										clipRule="evenodd"
									></path>
								</svg>
							</button>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
