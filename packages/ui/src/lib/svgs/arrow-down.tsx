const ArrowDown = ({ size = 24, ...props }: { size: number; className?: string }) => {
	return (
		<svg
			{...props}
			width={size}
			height={size}
			viewBox="0 0 24 24"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M1.91699 12.0026C1.91699 17.2926 6.21033 21.5859 11.5003 21.5859C16.7903 21.5859 21.0837 17.2926 21.0837 12.0026C21.0837 6.7126 16.7903 2.41927 11.5003 2.41927C6.21033 2.41927 1.91699 6.7126 1.91699 12.0026ZM12.2191 8.64844V13.6222L13.8674 11.9739C14.1453 11.6959 14.6053 11.6959 14.8832 11.9739C15.027 12.1176 15.0941 12.2997 15.0941 12.4818C15.0941 12.6639 15.027 12.8459 14.8832 12.9897L12.0082 15.8647C11.7303 16.1426 11.2703 16.1426 10.9924 15.8647L8.11741 12.9897C7.83949 12.7118 7.83949 12.2518 8.11741 11.9739C8.39533 11.6959 8.85533 11.6959 9.13324 11.9739L10.7816 13.6222V8.64844C10.7816 8.25552 11.1074 7.92969 11.5003 7.92969C11.8932 7.92969 12.2191 8.25552 12.2191 8.64844Z"
				fill="#FD1414"
				fill-opacity="0.75"
			/>
		</svg>
	);
};

export { ArrowDown };
