const ArrowUp = ({ size = 24, ...props }: { size: number; className?: string }) => {
	return (
		<svg
			{...props}
			width={size}
			height={size}
			viewBox="0 0 24 24"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M21.083 12.0013C21.083 6.7113 16.7897 2.41797 11.4997 2.41797C6.20967 2.41797 1.91634 6.7113 1.91634 12.0013C1.91634 17.2913 6.20967 21.5846 11.4997 21.5846C16.7897 21.5846 21.083 17.2913 21.083 12.0013ZM10.7809 15.3555V10.3817L9.13259 12.0301C8.85467 12.308 8.39467 12.308 8.11676 12.0301C7.97301 11.8863 7.90592 11.7042 7.90592 11.5221C7.90592 11.3401 7.97301 11.158 8.11676 11.0142L10.9918 8.13922C11.2697 7.8613 11.7297 7.8613 12.0076 8.13922L14.8826 11.0142C15.1605 11.2921 15.1605 11.7521 14.8826 12.0301C14.6047 12.308 14.1447 12.308 13.8668 12.0301L12.2184 10.3817V15.3555C12.2184 15.7484 11.8926 16.0742 11.4997 16.0742C11.1068 16.0742 10.7809 15.7484 10.7809 15.3555Z"
				fill="#94FF53"
			/>
		</svg>
	);
};

export { ArrowUp };
