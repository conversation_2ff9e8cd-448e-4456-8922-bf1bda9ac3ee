import { cn } from '../utils/utils';

interface ClocLogoProps {
	width?: number | string;
	height?: number | string;
	className?: string;
}

export function ClocLogo({ width = 160, height = 26, className }: ClocLogoProps) {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 160 26"
			xmlns="http://www.w3.org/2000/svg"
			className={cn(className, 'text-blue-950 dark:text-white')}
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M123.923 21.0663C123.923 20.882 123.933 20.7387 123.953 20.6363L126.779 0.489258L124.998 0.734952L122.234 20.3599C122.193 20.7694 122.172 21.0458 122.172 21.1891C122.172 22.0081 122.387 22.6326 122.817 23.0625C123.268 23.472 123.892 23.6768 124.691 23.6768C125.284 23.6768 125.899 23.5335 126.533 23.2468L126.134 21.8955C125.684 22.0593 125.295 22.1412 124.967 22.1412C124.598 22.1412 124.332 22.0593 124.168 21.8955C124.005 21.7112 123.923 21.4348 123.923 21.0663ZM119.66 8.59721C118.554 7.49158 117.111 6.93876 115.33 6.93876C113.507 6.93876 111.992 7.4711 110.784 8.53578C109.597 9.60046 108.727 10.9211 108.174 12.4976C107.621 14.0742 107.345 15.6405 107.345 17.1966C107.345 19.244 107.856 20.841 108.88 21.9876C109.904 23.1137 111.317 23.6768 113.118 23.6768C114.879 23.6768 116.486 23.124 117.94 22.0183L117.172 20.7592C116.517 21.2096 115.872 21.5474 115.238 21.7726C114.623 21.9979 113.948 22.1105 113.211 22.1105C111.941 22.1105 110.948 21.7112 110.231 20.9127C109.535 20.1142 109.187 18.9062 109.187 17.2887C109.187 15.9579 109.392 14.6168 109.802 13.2654C110.211 11.8936 110.866 10.747 111.767 9.82568C112.688 8.90432 113.866 8.44365 115.299 8.44365C115.995 8.44365 116.609 8.55626 117.142 8.78148C117.674 8.98622 118.176 9.30358 118.647 9.73355L119.66 8.59721ZM136.003 23.6768C134.16 23.6768 132.727 23.1035 131.704 21.9569C130.7 20.8103 130.199 19.2133 130.199 17.1659C130.199 15.6303 130.465 14.0844 130.997 12.5283C131.529 10.9518 132.389 9.63117 133.577 8.56649C134.785 7.48134 136.331 6.93876 138.214 6.93876C140.078 6.93876 141.511 7.50181 142.514 8.62792C143.517 9.75402 144.019 11.3408 144.019 13.3883C144.019 14.9034 143.753 16.4492 143.22 18.0258C142.709 19.6023 141.849 20.9434 140.641 22.049C139.433 23.1342 137.887 23.6768 136.003 23.6768ZM136.065 22.1719C137.518 22.1719 138.696 21.701 139.596 20.7592C140.518 19.8173 141.173 18.6707 141.562 17.3194C141.951 15.9476 142.146 14.5963 142.146 13.2654C142.146 11.6684 141.808 10.4604 141.132 9.64141C140.456 8.82243 139.474 8.41293 138.184 8.41293C136.71 8.41293 135.512 8.89409 134.59 9.85639C133.669 10.7982 133.014 11.955 132.625 13.3268C132.236 14.6987 132.041 16.0295 132.041 17.3194C132.041 18.9164 132.389 20.1244 133.086 20.9434C133.782 21.7624 134.775 22.1719 136.065 22.1719ZM160 8.59721C158.894 7.49158 157.451 6.93876 155.67 6.93876C153.847 6.93876 152.332 7.4711 151.124 8.53578C149.937 9.60046 149.067 10.9211 148.514 12.4976C147.961 14.0742 147.685 15.6405 147.685 17.1966C147.685 19.244 148.196 20.841 149.22 21.9876C150.244 23.1137 151.657 23.6768 153.458 23.6768C155.219 23.6768 156.826 23.124 158.28 22.0183L157.512 20.7592C156.857 21.2096 156.212 21.5474 155.577 21.7726C154.963 21.9979 154.288 22.1105 153.55 22.1105C152.281 22.1105 151.288 21.7112 150.571 20.9127C149.875 20.1142 149.527 18.9062 149.527 17.2887C149.527 15.9579 149.732 14.6168 150.141 13.2654C150.551 11.8936 151.206 10.747 152.107 9.82568C153.028 8.90432 154.206 8.44365 155.639 8.44365C156.335 8.44365 156.949 8.55626 157.482 8.78148C158.014 8.98622 158.516 9.30358 158.987 9.73355L160 8.59721ZM17.2256 16.8969L19.2731 22.4047C16.4818 24.1391 13.2518 25.037 9.96584 24.9918C3.28673 24.9918 0.0078563 20.8191 0.0078563 15.2705C-0.052926 13.535 0.239495 11.8053 0.867362 10.1862C1.49523 8.56706 2.44546 7.0924 3.66042 5.85159C4.87539 4.61079 6.32974 3.62988 7.93527 2.96806C9.5408 2.30627 11.2641 1.9773 13.0005 2.00153C18.4936 2.00153 21.3629 5.17219 21.3629 9.51134C21.3094 11.4851 20.8619 13.4286 20.0467 15.2269H7.29976C7.25881 17.2744 8.65254 18.8142 11.4386 18.8142C13.4998 18.6807 15.4923 18.0205 17.2256 16.8969ZM15.0948 10.8892C15.2182 10.5401 15.2872 10.1742 15.2995 9.80413C15.2995 8.59466 14.3562 7.71718 12.6348 7.71718C11.6484 7.69596 10.6814 7.99272 9.87673 8.56373C9.0721 9.13475 8.47263 9.94964 8.167 10.8878L15.0948 10.8892ZM26.2841 15.1449L27.0154 10.7574C27.2201 9.71469 27.1792 8.92078 26.2841 8.92078C25.5902 8.93131 24.9059 9.08541 24.2747 9.37388L25.1361 3.03698C26.7622 2.44071 28.4866 2.15766 30.2182 2.2022C33.7442 2.2022 35.9218 3.91159 35.0546 8.79332L34.1537 13.8841C33.7852 16.2679 34.3175 17.5157 35.916 17.5157C38.2559 17.5157 39.7287 15.6803 39.7287 10.3393C39.6877 7.7182 39.3297 5.11151 38.6625 2.57638H46.826C47.2107 4.89367 47.4166 7.23709 47.4417 9.58597C47.4417 19.3465 43.0148 24.8149 34.8645 24.8149C27.9236 24.8193 25.2604 21.1118 26.2841 15.1449ZM67.8827 16.8997L69.93 22.4076C67.1387 24.1418 63.9089 25.0398 60.623 24.9948C53.941 24.9948 50.6621 20.8223 50.6621 15.2737C50.6023 13.5379 50.8956 11.8078 51.5244 10.1887C52.1529 8.56954 53.104 7.09479 54.3194 5.85409C55.535 4.6134 56.9898 3.63266 58.5957 2.97093C60.2016 2.30919 61.9252 1.98056 63.662 2.00475C69.155 2.00475 72.0244 5.1754 72.0244 9.51456C71.9741 11.4876 71.53 13.4307 70.7183 15.2298H57.9701C57.9291 17.2773 59.3229 18.8171 62.1089 18.8171C64.1667 18.6804 66.1554 18.0194 67.8856 16.8969L67.8827 16.8997ZM65.7563 10.892C65.8796 10.5429 65.9486 10.177 65.961 9.80698C65.961 8.59751 65.0177 7.72003 63.2964 7.72003C62.3099 7.69882 61.3428 7.99557 60.5382 8.5666C59.7337 9.1376 59.1342 9.95249 58.8285 10.8906L65.7563 10.892ZM75.9204 24.4109L78.2604 10.7678C78.4242 9.72505 78.4242 8.93078 77.5292 8.93078C76.8266 8.97046 76.1353 9.12365 75.4817 9.38423L76.384 3.04733C78.0311 2.48283 79.7616 2.20044 81.5027 2.2122C83.9626 2.2122 85.7658 3.13082 86.2981 5.34208C86.846 4.38333 87.6397 3.58779 88.5972 3.03769C89.5546 2.4876 90.6415 2.20262 91.7458 2.2122C93.0042 2.19589 94.2461 2.49786 95.3567 3.08981L93.1848 10.4329C91.9687 9.85266 90.6377 9.55267 89.2904 9.55526C88.3595 9.59805 87.474 9.96949 86.7911 10.6036C86.1083 11.2376 85.6725 12.0935 85.5611 13.0186L83.594 24.4096L75.9204 24.4109ZM97.7626 15.2104C98.7814 15.2018 99.7798 15.4956 100.632 16.0544C101.483 16.6133 102.15 17.412 102.548 18.3499C102.946 19.2878 103.057 20.3226 102.866 21.3234C102.676 22.3243 102.193 23.2464 101.479 23.9726C100.764 24.699 99.8509 25.1975 98.8533 25.4045C97.8558 25.6116 96.8192 25.518 95.8748 25.136C94.9304 24.7539 94.1205 24.1006 93.5475 23.2582C92.9743 22.4158 92.6641 21.4224 92.6556 20.4036V20.3597C92.6527 19.6861 92.7826 19.019 93.0377 18.3956C93.2927 17.7722 93.6681 17.2051 94.1423 16.7268C94.6165 16.2484 95.1803 15.8682 95.8014 15.6077C96.4227 15.3474 97.089 15.2133 97.7626 15.2104ZM97.7626 15.8963C96.8799 15.8891 96.0149 16.1439 95.277 16.6283C94.539 17.1126 93.9612 17.805 93.6168 18.6177C93.2722 19.4303 93.1764 20.327 93.3414 21.1941C93.5064 22.0613 93.9248 22.8598 94.5437 23.4893C95.1628 24.1186 95.9544 24.5505 96.8186 24.7301C97.6829 24.9095 98.581 24.8285 99.3994 24.4975C100.218 24.1667 100.919 23.6006 101.416 22.8708C101.913 22.141 102.182 21.2807 102.19 20.3979V20.3597C102.192 19.7761 102.08 19.1978 101.858 18.6577C101.637 18.1175 101.312 17.6261 100.901 17.2116C100.49 16.7972 100.002 16.4675 99.4635 16.2419C98.9251 16.0162 98.3463 15.8988 97.7626 15.8963ZM96.3718 17.4437H98.0112C98.4868 17.4132 98.9553 17.5708 99.3158 17.8825C99.4677 18.0368 99.581 18.2245 99.6464 18.4309C99.7116 18.6374 99.7271 18.8565 99.6916 19.0701C99.6682 19.4048 99.538 19.723 99.3201 19.978C99.0848 20.2493 98.7822 20.4536 98.4427 20.5703L99.288 22.8898V22.9384H98.5377L97.7758 20.714H96.518L96.1363 22.9369H95.4285L96.3718 17.4437ZM96.9743 18.039L96.6146 20.1202H97.6471C97.9744 20.1293 98.2948 20.0257 98.5553 19.8274C98.6769 19.7351 98.7786 19.6192 98.8539 19.4864C98.9294 19.3537 98.977 19.2072 98.994 19.0554C99.0193 18.9259 99.0157 18.7924 98.9833 18.6645C98.951 18.5365 98.8908 18.4171 98.8068 18.3152C98.7088 18.2216 98.5927 18.1491 98.4658 18.1017C98.3388 18.0545 98.2035 18.0335 98.0683 18.0404L96.9743 18.039Z"
				fill="currentColor"
			/>
		</svg>
	);
}

export function ClocLogoIcon({ width = 60, height = 60, className }: ClocLogoProps) {
	return (
		<svg
			className={cn('size-8 mix-blend-difference', className)}
			width={width}
			height={height}
			viewBox="0 0 40 42"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0.301305 30.0417L0.581185 29.3889L4.20635 20.9338L4.20144 20.9242L0.334577 13.3102L0.0104603 12.6721L0.71239 12.4846L9.19627 10.2172L13.1922 1.54579L13.4927 0.893555L14.0859 1.31128L22.1152 6.96599L31.5808 4.42609L32.3695 4.21448L32.336 5.01473L31.9353 14.6145L39.3953 20.0675L40 20.5095L39.4223 20.9851L32.259 26.8807V26.8843L32.243 26.8973L31.9502 36.4851L31.9289 37.179L31.2344 37.0415L22.0143 35.2168L15.0316 40.6565L14.4535 41.1069L14.1245 40.4591L9.84483 32.0334L9.83811 32.0214L1.00836 30.1884L0.301305 30.0417ZM1.96323 29.1895L4.90946 22.3182L9.15843 30.6832L1.96323 29.1895ZM10.6516 30.9931L10.6745 31.0382L21.7022 33.9188L31.048 26.3458L30.7375 15.2001L30.7151 15.1837L30.7182 15.1073L21.8593 8.24458L10.05 11.2081L5.52582 20.9027L10.6516 30.9931ZM14.8657 39.2896L11.3843 32.4357L20.5803 34.8377L14.8657 39.2896ZM30.7765 35.7562L23.1683 34.2506L31.0167 27.8909L30.7765 35.7562ZM38.0756 20.5639L32.2172 25.3857L31.9583 16.0925L38.0756 20.5639ZM31.1081 5.7671L30.7787 13.6611L23.2948 7.86365L31.1081 5.7671ZM13.9812 2.67982L20.6111 7.34897L10.6813 9.84084L13.9812 2.67982ZM1.72897 13.4267L8.55107 11.6035L4.84103 19.5536L1.72897 13.4267ZM38.8031 18.0737L33.1326 6.90982L32.7945 7.07455L38.4651 18.2384L38.8031 18.0737ZM15.8809 1.53828L29.1717 4.18404L29.0965 4.54643L15.8057 1.90068L15.8809 1.53828ZM2.31222 10.6603L11.1714 2.64427L10.9153 2.37262L2.05606 10.3887L2.31222 10.6603ZM0.677161 15.1468L0.377393 27.2163L0 27.2072L0.29976 15.1379L0.677161 15.1468ZM12.6515 39.1303L2.54456 31.3383L2.3111 31.6289L12.4181 39.4208L12.6515 39.1303ZM29.3893 38.1013L17.4202 40.8246L17.3348 40.4644L29.3039 37.7409L29.3893 38.1013ZM32.8416 34.9313L38.7593 22.992L38.4196 22.8304L32.5019 34.7697L32.8416 34.9313Z"
				fill="white"
				pathLength="1"
				strokeDashoffset="0px"
				strokeDasharray="1px 1px"
			></path>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0.301305 30.0417L0.581185 29.3889L4.20635 20.9338L4.20144 20.9242L0.334577 13.3102L0.0104603 12.6721L0.71239 12.4846L9.19627 10.2172L13.1922 1.54579L13.4927 0.893555L14.0859 1.31128L22.1152 6.96599L31.5808 4.42609L32.3695 4.21448L32.336 5.01473L31.9353 14.6145L39.3953 20.0675L40 20.5095L39.4223 20.9851L32.259 26.8807V26.8843L32.243 26.8973L31.9502 36.4851L31.9289 37.179L31.2344 37.0415L22.0143 35.2168L15.0316 40.6565L14.4535 41.1069L14.1245 40.4591L9.84483 32.0334L9.83811 32.0214L1.00836 30.1884L0.301305 30.0417ZM1.96323 29.1895L4.90946 22.3182L9.15843 30.6832L1.96323 29.1895ZM10.6516 30.9931L10.6745 31.0382L21.7022 33.9188L31.048 26.3458L30.7375 15.2001L30.7151 15.1837L30.7182 15.1073L21.8593 8.24458L10.05 11.2081L5.52582 20.9027L10.6516 30.9931ZM14.8657 39.2896L11.3843 32.4357L20.5803 34.8377L14.8657 39.2896ZM30.7765 35.7562L23.1683 34.2506L31.0167 27.8909L30.7765 35.7562ZM38.0756 20.5639L32.2172 25.3857L31.9583 16.0925L38.0756 20.5639ZM31.1081 5.7671L30.7787 13.6611L23.2948 7.86365L31.1081 5.7671ZM13.9812 2.67982L20.6111 7.34897L10.6813 9.84084L13.9812 2.67982ZM1.72897 13.4267L8.55107 11.6035L4.84103 19.5536L1.72897 13.4267ZM38.8031 18.0737L33.1326 6.90982L32.7945 7.07455L38.4651 18.2384L38.8031 18.0737ZM15.8809 1.53828L29.1717 4.18404L29.0965 4.54643L15.8057 1.90068L15.8809 1.53828ZM2.31222 10.6603L11.1714 2.64427L10.9153 2.37262L2.05606 10.3887L2.31222 10.6603ZM0.677161 15.1468L0.377393 27.2163L0 27.2072L0.29976 15.1379L0.677161 15.1468ZM12.6515 39.1303L2.54456 31.3383L2.3111 31.6289L12.4181 39.4208L12.6515 39.1303ZM29.3893 38.1013L17.4202 40.8246L17.3348 40.4644L29.3039 37.7409L29.3893 38.1013ZM32.8416 34.9313L38.7593 22.992L38.4196 22.8304L32.5019 34.7697L32.8416 34.9313Z"
				fill="url(#paint0_linear_2814_32084)"
				pathLength="1"
				strokeDashoffset="0px"
				strokeDasharray="1px 1px"
			></path>
			<defs>
				<linearGradient
					id="paint0_linear_2814_32084"
					x1="0.807175"
					y1="7.04867"
					x2="40.6148"
					y2="7.78312"
					gradientUnits="userSpaceOnUse"
				>
					<stop stopColor="#FF1CF7"></stop>
					<stop offset="1" stopColor="#00F0FF"></stop>
				</linearGradient>
			</defs>
		</svg>
	);
}
