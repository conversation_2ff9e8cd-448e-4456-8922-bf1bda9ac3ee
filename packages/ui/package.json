{"name": "@cloc/ui", "version": "0.2.2", "sideEffects": false, "exports": {"./styles.css": "./dist/index.es.css", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js"}}, "license": "MIT", "scripts": {"lint": "eslint src/", "type-check": "tsc --noEmit", "build": "rollup -c ", "dev": "rollup -c --watch"}, "peerDependencies": {"@emotion/react": ">=11.13.5", "react": ">=18.0.0", "react-dom": ">=18.0.0", "theme-ui": ">=0.16.2"}, "optionalDependencies": {"@emotion/react": "^11.13.5", "theme-ui": "^0.16.2"}, "devDependencies": {"@cloc/eslint-config": "*", "@cloc/tailwind-config": "*", "@cloc/typescript-config": "*", "@headlessui/react": "^2.1.3", "@popperjs/core": "^2.11.8", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-alert-dialog": "^1.1.7", "add": "^2.0.6", "autoprefixer": "^10.4.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.395.0", "postcss": "^8.4.38", "react": "19.1.0", "react-dom": "19.1.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "react-day-picker": "^8.10.1", "react-popper-tooltip": "^4.4.2", "react-timer-hook": "^3.0.7", "recharts": "^2.12.7", "rollup": "^4.24.4", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "@radix-ui/react-menubar": "^1.1.12"}, "dependencies": {"@cloc/types": "*"}, "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "publishConfig": {"access": "restricted"}}