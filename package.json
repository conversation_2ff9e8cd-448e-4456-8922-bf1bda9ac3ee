{"name": "ever-cloc", "version": "0.1.0", "description": "Ever Cloc - Open Productivity & Time Tracking Platform", "license": "AGPL-3.0", "homepage": "https://cloc.ai", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-cloc.git"}, "bugs": {"url": "https://github.com/ever-co/ever-cloc/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "scripts": {"prepare:husky": "npx husky install .husky", "start:builder": "turbo run start --filter @cloc/builder", "start:example": "turbo run start --filter @cloc/example-base", "start:example-nextjs": "turbo run start --filter @cloc/example-nextjs", "start:example-nextjs-boilerplate": "turbo run start --filter @cloc/example-nextjs-boilerplate", "start:example-remix": "turbo run start --filter @cloc/example-remix", "start:example-saas": "turbo run start --filter @cloc/example-saas-starter", "start:example-vite": "turbo run start --filter @cloc/example-vite", "build": "turbo build", "build:storybook": "turbo run build --filter @cloc/storybook", "build:example": "turbo run build --filter @cloc/example-base", "build:example-nextjs": "turbo build --filter @cloc/example-nextjs", "build:atoms": "turbo run build --filter @cloc/atoms", "build:ui": "turbo run build --filter @cloc/ui", "build:api": "turbo run build --filter @cloc/api", "build:builder": "turbo run build --filter @cloc/builder", "build:types": "turbo run build --filter @cloc/types", "build:tracking": "turbo run build --filter @cloc/tracking", "build:example-nextjs-boilerplate": "turbo run build --filter @cloc/example-nextjs-boilerplate", "build:example-remix": "turbo run build --filter @cloc/example-remix", "build:example-saas": "turbo run build --filter @cloc/example-saas-starter", "build:example-vite": "turbo run build --filter @cloc/example-vite", "dev": "turbo dev --concurrency=15", "dev:example": "turbo dev --filter @cloc/example-base", "dev:example-nextjs": "turbo dev --filter @cloc/example-nextjs", "dev:example-nextjs-boilerplate": "turbo run dev --filter @cloc/example-nextjs-boilerplate", "dev:example-vite": "turbo run dev --filter @cloc/example-vite", "dev:atoms": "turbo dev --filter @cloc/atoms", "dev:ui": "turbo dev --filter @cloc/ui", "dev:tracking": "turbo run dev --filter @cloc/tracking", "dev:storybook": "turbo dev --filter @cloc/storybook", "dev:builder": "turbo dev --filter @cloc/builder", "dev:example-remix": "turbo dev --filter @cloc/example-remix", "dev:example-saas": "turbo run dev --filter @cloc/example-saas-starter", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "publish-packages": "changeset publish", "release": "yarn version-packages && yarn publish-packages"}, "workspaces": ["apps/*", "packages/*", "packages/toolkit/*", "packages/toolkit/examples/*"], "dependencies": {"@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "crypto-js": "^4.2.0", "grapesjs": "^0.22.5", "grapesjs-blocks-flexbox": "^1.0.1", "grapesjs-plugin-export": "^1.0.12", "jszip": "^3.10.1", "next-themes": "^0.4.4", "theme-ui": "^0.17.1", "tslib": "^2.8.1"}, "devDependencies": {"@changesets/cli": "^2.28.1", "@cloc/typescript-config": "*", "@rollup/plugin-alias": "^5.1.1", "@semantic-release/release-notes-generator": "^14.0.3", "@types/crypto-js": "^4.2.2", "eslint": "^9.24.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-react": "^7.31.8", "husky": "^9.0.11", "prettier": "^3.2.5", "prettier-eslint-cli": "^8.0.1", "prettier-plugin-tailwindcss": "^0.5.11", "pretty-quick": "^4.0.0", "rimraf": "^5.0.5", "rollup-plugin-esbuild": "^6.2.1", "semantic-release": "^22.0.12", "turbo": "^2.4.2"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged pretty-quick --no-verify --staged"}}, "engines": {"node": ">=18", "yarn": ">=1.13.0"}, "packageManager": "yarn@1.22.22", "resolutions": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}, "prettier": {"printWidth": 120, "singleQuote": true, "semi": true, "useTabs": true, "tabWidth": 4, "arrowParens": "always", "trailingComma": "none", "quoteProps": "as-needed", "trimTrailingWhitespace": true, "overrides": [{"files": "*.scss", "options": {"useTabs": false, "tabWidth": 2}}, {"files": "*.yml", "options": {"useTabs": false, "tabWidth": 2}}]}, "snyk": true}